'use client'

import React from 'react'
import Image from 'next/image'
import { UserAvatar } from './UserAvatar'
import { GalleryImage } from '@/app/dashboard2MockData'

interface GalleryItemProps {
  item: GalleryImage
}

export function GalleryItem({ item }: GalleryItemProps) {
  return (
    <div className="break-inside-avoid mb-4">
      {/* User info header */}
      <div className="flex items-center gap-2 mb-2">
        <UserAvatar src={item.avatar} alt={item.username} size="sm" />
        <span className="text-sm font-normal text-black">{item.username}</span>
      </div>
      
      {/* Images */}
      <div className="space-y-2">
        {item.images.map((image, index) => (
          <div key={index} className="rounded-2xl overflow-hidden border-none">
            <Image
              src={image}
              alt={`Generated by ${item.username}`}
              width={300}
              height={400}
              className="w-full h-auto object-cover"
            />
          </div>
        ))}
      </div>
    </div>
  )
}