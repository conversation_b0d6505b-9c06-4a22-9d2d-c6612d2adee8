# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.development
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Gensy specific
/uploads
/generated
/temp-files
service-account-key.json
google-credentials.json
google-cloud-credentials.json

# Sensitive files (API keys, credentials)
AccessKey.txt
sprint/*.json
*.json
!package.json
!package-lock.json
!tsconfig.json
!next.config.js

# Database
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# Certificates
*.pem
*.key
*.crt

# Backup files
*.backup
*.bak

# Test files
test-results/
playwright-report/
test-results.xml
test-*.js
analyze-*.js
check-*.js

# YoYo AI version control directory
.yoyo/

# clerk configuration (can include secrets)
/.clerk/

# Sentry Config File
.env.sentry-build-plugin
