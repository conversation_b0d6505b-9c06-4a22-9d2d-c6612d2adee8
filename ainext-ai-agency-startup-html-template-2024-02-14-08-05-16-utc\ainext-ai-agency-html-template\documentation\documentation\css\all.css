/*!
 * Bootstrap v3.3.6 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 *//*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
 
html {
	font-family: sans-serif;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%
}
body {
	margin: 0
}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
	display: block
}
audio, canvas, progress, video {
	display: inline-block;
	vertical-align: baseline
}
audio:not([controls]) {
	display: none;
	height: 0
}
[hidden], template {
	display:none
}
img {
	max-width: 100%;
}
a {
	background-color: transparent
}
a:active, a:hover {
	outline: 0
}
abbr[title] {
	border-bottom: 1px dotted
}
b, strong {
	font-weight: 700
}
dfn {
	font-style: italic
}
h1 {
	margin: .67em 0;
	font-size: 2em
}
mark {
	color: #000;
	background: #ff0
}
small {
	font-size: 80%
}
sub, sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline
}
sup {
	top: -.5em
}
sub {
	bottom: -.25em
}
img {
	border: 0
}
svg:not(:root) {
	overflow: hidden
}
figure {
	margin: 1em 40px
}
hr {
	height: 0;
	box-sizing: content-box
}
pre {
	overflow: auto
}
code, kbd, pre, samp {
	font-family: monospace;
	font-size: 1em
}
button, input, optgroup, select, textarea {
	margin: 0;
	font: inherit;
	color: inherit
}
button {
	overflow: visible
}
button, select {
	text-transform: none
}
button, html input[type=button], input[type=reset], input[type=submit] {
	-webkit-appearance: button;
	cursor: pointer
}
button[disabled], html input[disabled] {
	cursor: default
}
button::-moz-focus-inner, input::-moz-focus-inner {
	padding:0;
	border:0
}
input {
	line-height: normal
}
input[type=checkbox], input[type=radio] {
	box-sizing: border-box;
	padding: 0
}
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
	height:auto
}
input[type=search] {
	box-sizing: content-box;
	-webkit-appearance: textfield
}
input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration {
-webkit-appearance:none
}
fieldset {
	padding: .35em .625em .75em;
	margin: 0 2px;
	border: 1px solid silver
}
legend {
	padding: 0;
	border: 0
}
textarea {
	overflow: auto
}
optgroup {
	font-weight: 700
}
table {
	border-spacing: 0;
	border-collapse: collapse
}
td, th {
	padding: 0
}/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
	*, :after, :before {
		color: #000!important;
		text-shadow: none!important;
		background: 0 0!important;
		box-shadow: none!important
	}
	a, a:visited {
		text-decoration: underline
	}
	a[href]:after {
		content: " (" attr(href) ")"
	}
	abbr[title]:after {
		content: " (" attr(title) ")"
	}
	a[href^="#"]:after, a[href^="javascript:"]:after {
		content: ""
	}
	blockquote, pre {
		border: 1px solid #999;
		page-break-inside: avoid
	}
	thead {
		display: table-header-group
	}
	img, tr {
		page-break-inside: avoid
	}
	img {
		max-width: 100%!important
	}
	h2, h3, p {
		orphans: 3;
		widows: 3
	}
	h2, h3 {
		page-break-after: avoid
	}
	.navbar {
		display: none
	}
	.btn>.caret, .dropup>.btn>.caret {
		border-top-color: #000!important
	}
	.label {
		border: 1px solid #000
	}
	.table {
		border-collapse: collapse!important
	}
	.table td, .table th {
		background-color: #fff!important
	}
	.table-bordered td, .table-bordered th {
		border: 1px solid #ddd!important
	}
}
*, :after, :before {
	box-sizing: border-box
}
html {
	font-size: 10px;
	-webkit-tap-highlight-color: rgba(0,0,0,0)
}
body {
	font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
	font-size: 14px;
	line-height: 1.42857143;
	color: #333;
	background-color: #fff
}
button, input, select, textarea {
	font-family: inherit;
	font-size: inherit;
	line-height: inherit
}
a {
	color: #337ab7;
	text-decoration: none
}
a:focus, a:hover {
	color: #23527c;
	text-decoration: underline
}
a:focus {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}
figure {
	margin: 0
}
img {
	vertical-align: middle
}
.carousel-inner>.item>a>img, .carousel-inner>.item>img, .img-responsive, .thumbnail a>img, .thumbnail>img {
	display: block;
	max-width: 100%;
	height: auto
}
.img-rounded {
	border-radius: 6px
}
.img-thumbnail {
	display: inline-block;
	max-width: 100%;
	height: auto;
	padding: 4px;
	line-height: 1.42857143;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	transition: all .2s ease-in-out
}
.img-circle {
	border-radius: 50%
}
hr {
	margin-top: 20px;
	margin-bottom: 20px;
	border: 0;
	border-top: 1px solid #eee
}
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0,0,0,0);
	border: 0
}
.sr-only-focusable:active, .sr-only-focusable:focus {
	position: static;
	width: auto;
	height: auto;
	margin: 0;
	overflow: visible;
	clip: auto
}
[role=button] {
cursor:pointer
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
	font-family: inherit;
	font-weight: 500;
	line-height: 1.1;
	color: inherit
}
.h1 .small, .h1 small, .h2 .small, .h2 small, .h3 .small, .h3 small, .h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {
	font-weight: 400;
	line-height: 1;
	color: #777
}
.h1, .h2, .h3, h1, h2, h3 {
	margin-top: 20px;
	margin-bottom: 10px
}
.h1 .small, .h1 small, .h2 .small, .h2 small, .h3 .small, .h3 small, h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small {
	font-size: 65%
}
.h4, .h5, .h6, h4, h5, h6 {
	margin-top: 10px;
	margin-bottom: 10px
}
.h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {
	font-size: 75%
}
.h1, h1 {
	font-size: 36px
}
.h2, h2 {
	font-size: 30px
}
.h3, h3 {
	font-size: 24px
}
.h4, h4 {
	font-size: 18px
}
.h5, h5 {
	font-size: 14px
}
.h6, h6 {
	font-size: 12px
}
p {
	margin: 0 0 10px
}
.lead {
	margin-bottom: 20px;
	font-size: 16px;
	font-weight: 300;
	line-height: 1.4
}
@media (min-width:768px) {
.lead {
	font-size: 21px
}
}
.small, small {
	font-size: 85%
}
.mark, mark {
	padding: .2em;
	background-color: #fcf8e3
}
.text-left {
	text-align: left
}
.text-right {
	text-align: right
}
.text-center {
	text-align: center
}
.text-justify {
	text-align: justify
}
.text-nowrap {
	white-space: nowrap
}
.text-lowercase {
	text-transform: lowercase
}
.text-uppercase {
	text-transform: uppercase
}
.text-capitalize {
	text-transform: capitalize
}
.text-muted {
	color: #777
}
.text-primary {
	color: #337ab7
}
a.text-primary:focus, a.text-primary:hover {
	color: #286090
}
.text-success {
	color: #3c763d
}
a.text-success:focus, a.text-success:hover {
	color: #2b542c
}
.text-info {
	color: #31708f
}
a.text-info:focus, a.text-info:hover {
	color: #245269
}
.text-warning {
	color: #8a6d3b
}
a.text-warning:focus, a.text-warning:hover {
	color: #66512c
}
.text-danger {
	color: #a94442
}
a.text-danger:focus, a.text-danger:hover {
	color: #843534
}
.bg-primary {
	color: #fff;
	background-color: #337ab7
}
a.bg-primary:focus, a.bg-primary:hover {
	background-color: #286090
}
.bg-success {
	background-color: #dff0d8
}
a.bg-success:focus, a.bg-success:hover {
	background-color: #c1e2b3
}
.bg-info {
	background-color: #d9edf7
}
a.bg-info:focus, a.bg-info:hover {
	background-color: #afd9ee
}
.bg-warning {
	background-color: #fcf8e3
}
a.bg-warning:focus, a.bg-warning:hover {
	background-color: #f7ecb5
}
.bg-danger {
	background-color: #f2dede
}
a.bg-danger:focus, a.bg-danger:hover {
	background-color: #e4b9b9
}
.page-header {
	padding-bottom: 9px;
	margin: 0 0 20px;
	border-bottom: 1px solid #eee
}
ol, ul {
	margin-top: 0;
	margin-bottom: 10px
}
ol ol, ol ul, ul ol, ul ul {
	margin-bottom: 0
}
.list-inline, .list-unstyled {
	padding-left: 0;
	list-style: none
}
.list-inline {
	margin-left: -5px
}
.list-inline>li {
	display: inline-block;
	padding-right: 5px;
	padding-left: 5px
}
dl {
	margin-top: 0;
	margin-bottom: 20px
}
dd, dt {
	line-height: 1.42857143
}
dt {
	font-weight: 700
}
dd {
	margin-left: 0
}
@media (min-width:768px) {
	.dl-horizontal dt {
		float: left;
		width: 160px;
		overflow: hidden;
		clear: left;
		text-align: right;
		text-overflow: ellipsis;
		white-space: nowrap
	}
	.dl-horizontal dd {
		margin-left: 180px
	}
}
abbr[data-original-title], abbr[title] {
	cursor: help;
	border-bottom: 1px dotted #777
}
.initialism {
	font-size: 90%;
	text-transform: uppercase
}
blockquote {
	padding: 10px 20px;
	margin: 0 0 20px;
	font-size: 17.5px;
	border-left: 5px solid #eee
}
blockquote ol:last-child, blockquote p:last-child, blockquote ul:last-child {
	margin-bottom: 0
}
blockquote .small, blockquote footer, blockquote small {
	display: block;
	font-size: 80%;
	line-height: 1.42857143;
	color: #777
}
blockquote .small:before, blockquote footer:before, blockquote small:before {
	content: '\2014 \00A0'
}
.blockquote-reverse, blockquote.pull-right {
	padding-right: 15px;
	padding-left: 0;
	text-align: right;
	border-right: 5px solid #eee;
	border-left: 0
}
.blockquote-reverse .small:before, .blockquote-reverse footer:before, .blockquote-reverse small:before, blockquote.pull-right .small:before, blockquote.pull-right footer:before, blockquote.pull-right small:before {
	content: ''
}
.blockquote-reverse .small:after, .blockquote-reverse footer:after, .blockquote-reverse small:after, blockquote.pull-right .small:after, blockquote.pull-right footer:after, blockquote.pull-right small:after {
	content: '\00A0 \2014'
}
address {
	margin-bottom: 20px;
	font-style: normal;
	line-height: 1.42857143
}
code, kbd, pre, samp {
	font-family: Menlo, Monaco, Consolas, Courier New, monospace
}
code {
	color: #c7254e;
	background-color: #f9f2f4;
	border-radius: 4px
}
code, kbd {
	padding: 2px 4px;
	font-size: 90%
}
kbd {
	color: #fff;
	background-color: #333;
	border-radius: 3px;
	box-shadow: inset 0 -1px 0 rgba(0,0,0,.25)
}
kbd kbd {
	padding: 0;
	font-size: 100%;
	font-weight: 700;
	box-shadow: none
}
pre {
	display: block;
	padding: 9.5px;
	margin: 0 0 10px;
	font-size: 13px;
	line-height: 1.42857143;
	color: #333;
	word-break: break-all;
	word-wrap: break-word;
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	border-radius: 4px
}
pre code {
	padding: 0;
	font-size: inherit;
	color: inherit;
	white-space: pre-wrap;
	background-color: transparent;
	border-radius: 0
}
.pre-scrollable {
	max-height: 340px;
	overflow-y: scroll
}
.container {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto
}
@media (min-width:768px) {
	.container {
		width: 750px
	}
}
@media (min-width:992px) {
	.container {
		width: 970px
	}
}
@media (min-width:1200px) {
	.container {
		width: 1170px
	}
}
.container-fluid {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto
}
.row {
	margin-right: -15px;
	margin-left: -15px
}
.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
	position: relative;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
	float: left
}
.col-xs-12 {
	width: 100%
}
.col-xs-11 {
	width: 91.66666667%
}
.col-xs-10 {
	width: 83.33333333%
}
.col-xs-9 {
	width: 75%
}
.col-xs-8 {
	width: 66.66666667%
}
.col-xs-7 {
	width: 58.33333333%
}
.col-xs-6 {
	width: 50%
}
.col-xs-5 {
	width: 41.66666667%
}
.col-xs-4 {
	width: 33.33333333%
}
.col-xs-3 {
	width: 25%
}
.col-xs-2 {
	width: 16.66666667%
}
.col-xs-1 {
	width: 8.33333333%
}
.col-xs-pull-12 {
	right: 100%
}
.col-xs-pull-11 {
	right: 91.66666667%
}
.col-xs-pull-10 {
	right: 83.33333333%
}
.col-xs-pull-9 {
	right: 75%
}
.col-xs-pull-8 {
	right: 66.66666667%
}
.col-xs-pull-7 {
	right: 58.33333333%
}
.col-xs-pull-6 {
	right: 50%
}
.col-xs-pull-5 {
	right: 41.66666667%
}
.col-xs-pull-4 {
	right: 33.33333333%
}
.col-xs-pull-3 {
	right: 25%
}
.col-xs-pull-2 {
	right: 16.66666667%
}
.col-xs-pull-1 {
	right: 8.33333333%
}
.col-xs-pull-0 {
	right: auto
}
.col-xs-push-12 {
	left: 100%
}
.col-xs-push-11 {
	left: 91.66666667%
}
.col-xs-push-10 {
	left: 83.33333333%
}
.col-xs-push-9 {
	left: 75%
}
.col-xs-push-8 {
	left: 66.66666667%
}
.col-xs-push-7 {
	left: 58.33333333%
}
.col-xs-push-6 {
	left: 50%
}
.col-xs-push-5 {
	left: 41.66666667%
}
.col-xs-push-4 {
	left: 33.33333333%
}
.col-xs-push-3 {
	left: 25%
}
.col-xs-push-2 {
	left: 16.66666667%
}
.col-xs-push-1 {
	left: 8.33333333%
}
.col-xs-push-0 {
	left: auto
}
.col-xs-offset-12 {
	margin-left: 100%
}
.col-xs-offset-11 {
	margin-left: 91.66666667%
}
.col-xs-offset-10 {
	margin-left: 83.33333333%
}
.col-xs-offset-9 {
	margin-left: 75%
}
.col-xs-offset-8 {
	margin-left: 66.66666667%
}
.col-xs-offset-7 {
	margin-left: 58.33333333%
}
.col-xs-offset-6 {
	margin-left: 50%
}
.col-xs-offset-5 {
	margin-left: 41.66666667%
}
.col-xs-offset-4 {
	margin-left: 33.33333333%
}
.col-xs-offset-3 {
	margin-left: 25%
}
.col-xs-offset-2 {
	margin-left: 16.66666667%
}
.col-xs-offset-1 {
	margin-left: 8.33333333%
}
.col-xs-offset-0 {
	margin-left: 0
}
@media (min-width:768px) {
	.col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
		float: left
	}
	.col-sm-12 {
		width: 100%
	}
	.col-sm-11 {
		width: 91.66666667%
	}
	.col-sm-10 {
		width: 83.33333333%
	}
	.col-sm-9 {
		width: 75%
	}
	.col-sm-8 {
		width: 66.66666667%
	}
	.col-sm-7 {
		width: 58.33333333%
	}
	.col-sm-6 {
		width: 50%
	}
	.col-sm-5 {
		width: 41.66666667%
	}
	.col-sm-4 {
		width: 33.33333333%
	}
	.col-sm-3 {
		width: 25%
	}
	.col-sm-2 {
		width: 16.66666667%
	}
	.col-sm-1 {
		width: 8.33333333%
	}
	.col-sm-pull-12 {
		right: 100%
	}
	.col-sm-pull-11 {
		right: 91.66666667%
	}
	.col-sm-pull-10 {
		right: 83.33333333%
	}
	.col-sm-pull-9 {
		right: 75%
	}
	.col-sm-pull-8 {
		right: 66.66666667%
	}
	.col-sm-pull-7 {
		right: 58.33333333%
	}
	.col-sm-pull-6 {
		right: 50%
	}
	.col-sm-pull-5 {
		right: 41.66666667%
	}
	.col-sm-pull-4 {
		right: 33.33333333%
	}
	.col-sm-pull-3 {
		right: 25%
	}
	.col-sm-pull-2 {
		right: 16.66666667%
	}
	.col-sm-pull-1 {
		right: 8.33333333%
	}
	.col-sm-pull-0 {
		right: auto
	}
	.col-sm-push-12 {
		left: 100%
	}
	.col-sm-push-11 {
		left: 91.66666667%
	}
	.col-sm-push-10 {
		left: 83.33333333%
	}
	.col-sm-push-9 {
		left: 75%
	}
	.col-sm-push-8 {
		left: 66.66666667%
	}
	.col-sm-push-7 {
		left: 58.33333333%
	}
	.col-sm-push-6 {
		left: 50%
	}
	.col-sm-push-5 {
		left: 41.66666667%
	}
	.col-sm-push-4 {
		left: 33.33333333%
	}
	.col-sm-push-3 {
		left: 25%
	}
	.col-sm-push-2 {
		left: 16.66666667%
	}
	.col-sm-push-1 {
		left: 8.33333333%
	}
	.col-sm-push-0 {
		left: auto
	}
	.col-sm-offset-12 {
		margin-left: 100%
	}
	.col-sm-offset-11 {
		margin-left: 91.66666667%
	}
	.col-sm-offset-10 {
		margin-left: 83.33333333%
	}
	.col-sm-offset-9 {
		margin-left: 75%
	}
	.col-sm-offset-8 {
		margin-left: 66.66666667%
	}
	.col-sm-offset-7 {
		margin-left: 58.33333333%
	}
	.col-sm-offset-6 {
		margin-left: 50%
	}
	.col-sm-offset-5 {
		margin-left: 41.66666667%
	}
	.col-sm-offset-4 {
		margin-left: 33.33333333%
	}
	.col-sm-offset-3 {
		margin-left: 25%
	}
	.col-sm-offset-2 {
		margin-left: 16.66666667%
	}
	.col-sm-offset-1 {
		margin-left: 8.33333333%
	}
	.col-sm-offset-0 {
		margin-left: 0
	}
}
@media (min-width:992px) {
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
	float: left
}
.col-md-12 {
	width: 100%
}
.col-md-11 {
	width: 91.66666667%
}
.col-md-10 {
	width: 83.33333333%
}
.col-md-9 {
	width: 75%
}
.col-md-8 {
	width: 66.66666667%
}
.col-md-7 {
	width: 58.33333333%
}
.col-md-6 {
	width: 50%
}
.col-md-5 {
	width: 41.66666667%
}
.col-md-4 {
	width: 33.33333333%
}
.col-md-3 {
	width: 25%
}
.col-md-2 {
	width: 16.66666667%
}
.col-md-1 {
	width: 8.33333333%
}
.col-md-pull-12 {
	right: 100%
}
.col-md-pull-11 {
	right: 91.66666667%
}
.col-md-pull-10 {
	right: 83.33333333%
}
.col-md-pull-9 {
	right: 75%
}
.col-md-pull-8 {
	right: 66.66666667%
}
.col-md-pull-7 {
	right: 58.33333333%
}
.col-md-pull-6 {
	right: 50%
}
.col-md-pull-5 {
	right: 41.66666667%
}
.col-md-pull-4 {
	right: 33.33333333%
}
.col-md-pull-3 {
	right: 25%
}
.col-md-pull-2 {
	right: 16.66666667%
}
.col-md-pull-1 {
	right: 8.33333333%
}
.col-md-pull-0 {
	right: auto
}
.col-md-push-12 {
	left: 100%
}
.col-md-push-11 {
	left: 91.66666667%
}
.col-md-push-10 {
	left: 83.33333333%
}
.col-md-push-9 {
	left: 75%
}
.col-md-push-8 {
	left: 66.66666667%
}
.col-md-push-7 {
	left: 58.33333333%
}
.col-md-push-6 {
	left: 50%
}
.col-md-push-5 {
	left: 41.66666667%
}
.col-md-push-4 {
	left: 33.33333333%
}
.col-md-push-3 {
	left: 25%
}
.col-md-push-2 {
	left: 16.66666667%
}
.col-md-push-1 {
	left: 8.33333333%
}
.col-md-push-0 {
	left: auto
}
.col-md-offset-12 {
	margin-left: 100%
}
.col-md-offset-11 {
	margin-left: 91.66666667%
}
.col-md-offset-10 {
	margin-left: 83.33333333%
}
.col-md-offset-9 {
	margin-left: 75%
}
.col-md-offset-8 {
	margin-left: 66.66666667%
}
.col-md-offset-7 {
	margin-left: 58.33333333%
}
.col-md-offset-6 {
	margin-left: 50%
}
.col-md-offset-5 {
	margin-left: 41.66666667%
}
.col-md-offset-4 {
	margin-left: 33.33333333%
}
.col-md-offset-3 {
	margin-left: 25%
}
.col-md-offset-2 {
	margin-left: 16.66666667%
}
.col-md-offset-1 {
	margin-left: 8.33333333%
}
.col-md-offset-0 {
	margin-left: 0
}
}
@media (min-width:1200px) {
.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
	float: left
}
.col-lg-12 {
	width: 100%
}
.col-lg-11 {
	width: 91.66666667%
}
.col-lg-10 {
	width: 83.33333333%
}
.col-lg-9 {
	width: 75%
}
.col-lg-8 {
	width: 66.66666667%
}
.col-lg-7 {
	width: 58.33333333%
}
.col-lg-6 {
	width: 50%
}
.col-lg-5 {
	width: 41.66666667%
}
.col-lg-4 {
	width: 33.33333333%
}
.col-lg-3 {
	width: 25%
}
.col-lg-2 {
	width: 16.66666667%
}
.col-lg-1 {
	width: 8.33333333%
}
.col-lg-pull-12 {
	right: 100%
}
.col-lg-pull-11 {
	right: 91.66666667%
}
.col-lg-pull-10 {
	right: 83.33333333%
}
.col-lg-pull-9 {
	right: 75%
}
.col-lg-pull-8 {
	right: 66.66666667%
}
.col-lg-pull-7 {
	right: 58.33333333%
}
.col-lg-pull-6 {
	right: 50%
}
.col-lg-pull-5 {
	right: 41.66666667%
}
.col-lg-pull-4 {
	right: 33.33333333%
}
.col-lg-pull-3 {
	right: 25%
}
.col-lg-pull-2 {
	right: 16.66666667%
}
.col-lg-pull-1 {
	right: 8.33333333%
}
.col-lg-pull-0 {
	right: auto
}
.col-lg-push-12 {
	left: 100%
}
.col-lg-push-11 {
	left: 91.66666667%
}
.col-lg-push-10 {
	left: 83.33333333%
}
.col-lg-push-9 {
	left: 75%
}
.col-lg-push-8 {
	left: 66.66666667%
}
.col-lg-push-7 {
	left: 58.33333333%
}
.col-lg-push-6 {
	left: 50%
}
.col-lg-push-5 {
	left: 41.66666667%
}
.col-lg-push-4 {
	left: 33.33333333%
}
.col-lg-push-3 {
	left: 25%
}
.col-lg-push-2 {
	left: 16.66666667%
}
.col-lg-push-1 {
	left: 8.33333333%
}
.col-lg-push-0 {
	left: auto
}
.col-lg-offset-12 {
	margin-left: 100%
}
.col-lg-offset-11 {
	margin-left: 91.66666667%
}
.col-lg-offset-10 {
	margin-left: 83.33333333%
}
.col-lg-offset-9 {
	margin-left: 75%
}
.col-lg-offset-8 {
	margin-left: 66.66666667%
}
.col-lg-offset-7 {
	margin-left: 58.33333333%
}
.col-lg-offset-6 {
	margin-left: 50%
}
.col-lg-offset-5 {
	margin-left: 41.66666667%
}
.col-lg-offset-4 {
	margin-left: 33.33333333%
}
.col-lg-offset-3 {
	margin-left: 25%
}
.col-lg-offset-2 {
	margin-left: 16.66666667%
}
.col-lg-offset-1 {
	margin-left: 8.33333333%
}
.col-lg-offset-0 {
	margin-left: 0
}
}
table {
	background-color: transparent
}
caption {
	padding-top: 8px;
	padding-bottom: 8px;
	color: #777
}
caption, th {
	text-align: left
}
.table {
	width: 100%;
	max-width: 100%;
	margin-bottom: 20px
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	padding: 8px;
	line-height: 1.42857143;
	vertical-align: top;
	border-top: 1px solid #ddd
}
.table>thead>tr>th {
	vertical-align: bottom;
	border-bottom: 2px solid #ddd
}
.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
	border-top: 0
}
.table>tbody+tbody {
	border-top: 2px solid #ddd
}
.table .table {
	background-color: #fff
}
.table-condensed>tbody>tr>td, .table-condensed>tbody>tr>th, .table-condensed>tfoot>tr>td, .table-condensed>tfoot>tr>th, .table-condensed>thead>tr>td, .table-condensed>thead>tr>th {
	padding: 5px
}
.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	border: 1px solid #ddd
}
.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	border-bottom-width: 2px
}
.table-striped>tbody>tr:nth-of-type(odd) {
	background-color: #f9f9f9
}
.table-hover>tbody>tr:hover {
	background-color: #f5f5f5
}
table col[class*=col-] {
	position: static;
	display: table-column;
	float: none
}
table td[class*=col-], table th[class*=col-] {
	position: static;
	display: table-cell;
	float: none
}
.table>tbody>tr.active>td, .table>tbody>tr.active>th, .table>tbody>tr>td.active, .table>tbody>tr>th.active, .table>tfoot>tr.active>td, .table>tfoot>tr.active>th, .table>tfoot>tr>td.active, .table>tfoot>tr>th.active, .table>thead>tr.active>td, .table>thead>tr.active>th, .table>thead>tr>td.active, .table>thead>tr>th.active {
	background-color: #f5f5f5
}
.table-hover>tbody>tr.active:hover>td, .table-hover>tbody>tr.active:hover>th, .table-hover>tbody>tr:hover>.active, .table-hover>tbody>tr>td.active:hover, .table-hover>tbody>tr>th.active:hover {
	background-color: #e8e8e8
}
.table>tbody>tr.success>td, .table>tbody>tr.success>th, .table>tbody>tr>td.success, .table>tbody>tr>th.success, .table>tfoot>tr.success>td, .table>tfoot>tr.success>th, .table>tfoot>tr>td.success, .table>tfoot>tr>th.success, .table>thead>tr.success>td, .table>thead>tr.success>th, .table>thead>tr>td.success, .table>thead>tr>th.success {
	background-color: #dff0d8
}
.table-hover>tbody>tr.success:hover>td, .table-hover>tbody>tr.success:hover>th, .table-hover>tbody>tr:hover>.success, .table-hover>tbody>tr>td.success:hover, .table-hover>tbody>tr>th.success:hover {
	background-color: #d0e9c6
}
.table>tbody>tr.info>td, .table>tbody>tr.info>th, .table>tbody>tr>td.info, .table>tbody>tr>th.info, .table>tfoot>tr.info>td, .table>tfoot>tr.info>th, .table>tfoot>tr>td.info, .table>tfoot>tr>th.info, .table>thead>tr.info>td, .table>thead>tr.info>th, .table>thead>tr>td.info, .table>thead>tr>th.info {
	background-color: #d9edf7
}
.table-hover>tbody>tr.info:hover>td, .table-hover>tbody>tr.info:hover>th, .table-hover>tbody>tr:hover>.info, .table-hover>tbody>tr>td.info:hover, .table-hover>tbody>tr>th.info:hover {
	background-color: #c4e3f3
}
.table>tbody>tr.warning>td, .table>tbody>tr.warning>th, .table>tbody>tr>td.warning, .table>tbody>tr>th.warning, .table>tfoot>tr.warning>td, .table>tfoot>tr.warning>th, .table>tfoot>tr>td.warning, .table>tfoot>tr>th.warning, .table>thead>tr.warning>td, .table>thead>tr.warning>th, .table>thead>tr>td.warning, .table>thead>tr>th.warning {
	background-color: #fcf8e3
}
.table-hover>tbody>tr.warning:hover>td, .table-hover>tbody>tr.warning:hover>th, .table-hover>tbody>tr:hover>.warning, .table-hover>tbody>tr>td.warning:hover, .table-hover>tbody>tr>th.warning:hover {
	background-color: #faf2cc
}
.table>tbody>tr.danger>td, .table>tbody>tr.danger>th, .table>tbody>tr>td.danger, .table>tbody>tr>th.danger, .table>tfoot>tr.danger>td, .table>tfoot>tr.danger>th, .table>tfoot>tr>td.danger, .table>tfoot>tr>th.danger, .table>thead>tr.danger>td, .table>thead>tr.danger>th, .table>thead>tr>td.danger, .table>thead>tr>th.danger {
	background-color: #f2dede
}
.table-hover>tbody>tr.danger:hover>td, .table-hover>tbody>tr.danger:hover>th, .table-hover>tbody>tr:hover>.danger, .table-hover>tbody>tr>td.danger:hover, .table-hover>tbody>tr>th.danger:hover {
	background-color: #ebcccc
}
.table-responsive {
	min-height: .01%;
	overflow-x: auto
}
@media screen and (max-width:767px) {
.table-responsive {
	width: 100%;
	margin-bottom: 15px;
	overflow-y: hidden;
	-ms-overflow-style: -ms-autohiding-scrollbar;
	border: 1px solid #ddd
}
.table-responsive>.table {
	margin-bottom: 0
}
.table-responsive>.table>tbody>tr>td, .table-responsive>.table>tbody>tr>th, .table-responsive>.table>tfoot>tr>td, .table-responsive>.table>tfoot>tr>th, .table-responsive>.table>thead>tr>td, .table-responsive>.table>thead>tr>th {
	white-space: nowrap
}
.table-responsive>.table-bordered {
	border: 0
}
.table-responsive>.table-bordered>tbody>tr>td:first-child, .table-responsive>.table-bordered>tbody>tr>th:first-child, .table-responsive>.table-bordered>tfoot>tr>td:first-child, .table-responsive>.table-bordered>tfoot>tr>th:first-child, .table-responsive>.table-bordered>thead>tr>td:first-child, .table-responsive>.table-bordered>thead>tr>th:first-child {
	border-left: 0
}
.table-responsive>.table-bordered>tbody>tr>td:last-child, .table-responsive>.table-bordered>tbody>tr>th:last-child, .table-responsive>.table-bordered>tfoot>tr>td:last-child, .table-responsive>.table-bordered>tfoot>tr>th:last-child, .table-responsive>.table-bordered>thead>tr>td:last-child, .table-responsive>.table-bordered>thead>tr>th:last-child {
	border-right: 0
}
.table-responsive>.table-bordered>tbody>tr:last-child>td, .table-responsive>.table-bordered>tbody>tr:last-child>th, .table-responsive>.table-bordered>tfoot>tr:last-child>td, .table-responsive>.table-bordered>tfoot>tr:last-child>th {
	border-bottom: 0
}
}
fieldset {
	min-width: 0;
	margin: 0
}
fieldset, legend {
	padding: 0;
	border: 0
}
legend {
	display: block;
	width: 100%;
	margin-bottom: 20px;
	font-size: 21px;
	line-height: inherit;
	color: #333;
	border-bottom: 1px solid #e5e5e5
}
label {
	display: inline-block;
	max-width: 100%;
	margin-bottom: 5px;
	font-weight: 700
}
input[type=search] {
	box-sizing: border-box
}
input[type=checkbox], input[type=radio] {
	margin: 4px 0 0;
	margin-top: 1px\9;
	line-height: normal
}
input[type=file] {
	display: block
}
input[type=range] {
	display: block;
	width: 100%
}
select[multiple], select[size] {
	height: auto
}
input[type=checkbox]:focus, input[type=file]:focus, input[type=radio]:focus {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}
output {
	padding-top: 7px
}
.form-control, output {
	display: block;
	font-size: 14px;
	line-height: 1.42857143;
	color: #555
}
.form-control {
	width: 100%;
	height: 34px;
	padding: 6px 12px;
	background-color: #fff;
	background-image: none;
	border: 1px solid #ccc;
	border-radius: 4px;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}
.form-control:focus {
	border-color: #66afe9;
	outline: 0;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6)
}
.form-control::-moz-placeholder {
color:#999;
opacity:1
}
.form-control:-ms-input-placeholder {
color:#999
}
.form-control::-webkit-input-placeholder {
color:#999
}
.form-control::-ms-expand {
background-color:transparent;
border:0
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
	background-color: #eee;
	opacity: 1
}
.form-control[disabled], fieldset[disabled] .form-control {
	cursor: not-allowed
}
textarea.form-control {
	height: auto
}
input[type=search] {
	-webkit-appearance: none
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
input[type=date].form-control, input[type=datetime-local].form-control, input[type=month].form-control, input[type=time].form-control {
	line-height: 34px
}
.input-group-sm input[type=date], .input-group-sm input[type=datetime-local], .input-group-sm input[type=month], .input-group-sm input[type=time], input[type=date].input-sm, input[type=datetime-local].input-sm, input[type=month].input-sm, input[type=time].input-sm {
	line-height: 30px
}
.input-group-lg input[type=date], .input-group-lg input[type=datetime-local], .input-group-lg input[type=month], .input-group-lg input[type=time], input[type=date].input-lg, input[type=datetime-local].input-lg, input[type=month].input-lg, input[type=time].input-lg {
	line-height: 46px
}
}
.form-group {
	margin-bottom: 15px
}
.checkbox, .radio {
	position: relative;
	display: block;
	margin-top: 10px;
	margin-bottom: 10px
}
.checkbox label, .radio label {
	min-height: 20px;
	padding-left: 20px;
	margin-bottom: 0;
	font-weight: 400;
	cursor: pointer
}
.checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio] {
	position: absolute;
	margin-top: 4px\9;
	margin-left: -20px
}
.checkbox+.checkbox, .radio+.radio {
	margin-top: -5px
}
.checkbox-inline, .radio-inline {
	position: relative;
	display: inline-block;
	padding-left: 20px;
	margin-bottom: 0;
	font-weight: 400;
	vertical-align: middle;
	cursor: pointer
}
.checkbox-inline+.checkbox-inline, .radio-inline+.radio-inline {
	margin-top: 0;
	margin-left: 10px
}
.checkbox-inline.disabled, .checkbox.disabled label, .radio-inline.disabled, .radio.disabled label, fieldset[disabled] .checkbox label, fieldset[disabled] .checkbox-inline, fieldset[disabled] .radio label, fieldset[disabled] .radio-inline, fieldset[disabled] input[type=checkbox], fieldset[disabled] input[type=radio], input[type=checkbox].disabled, input[type=checkbox][disabled], input[type=radio].disabled, input[type=radio][disabled] {
	cursor: not-allowed
}
.form-control-static {
	min-height: 34px;
	padding-top: 7px;
	padding-bottom: 7px;
	margin-bottom: 0
}
.form-control-static.input-lg, .form-control-static.input-sm {
	padding-right: 0;
	padding-left: 0
}
.input-sm {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}
select.input-sm {
	height: 30px;
	line-height: 30px
}
select[multiple].input-sm, textarea.input-sm {
	height: auto
}
.form-group-sm .form-control {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}
.form-group-sm select.form-control {
	height: 30px;
	line-height: 30px
}
.form-group-sm select[multiple].form-control, .form-group-sm textarea.form-control {
	height: auto
}
.form-group-sm .form-control-static {
	height: 30px;
	min-height: 32px;
	padding: 6px 10px;
	font-size: 12px;
	line-height: 1.5
}
.input-lg {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}
select.input-lg {
	height: 46px;
	line-height: 46px
}
select[multiple].input-lg, textarea.input-lg {
	height: auto
}
.form-group-lg .form-control {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}
.form-group-lg select.form-control {
	height: 46px;
	line-height: 46px
}
.form-group-lg select[multiple].form-control, .form-group-lg textarea.form-control {
	height: auto
}
.form-group-lg .form-control-static {
	height: 46px;
	min-height: 38px;
	padding: 11px 16px;
	font-size: 18px;
	line-height: 1.3333333
}
.has-feedback {
	position: relative
}
.has-feedback .form-control {
	padding-right: 42.5px
}
.form-control-feedback {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
	display: block;
	width: 34px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	pointer-events: none
}
.form-group-lg .form-control+.form-control-feedback, .input-group-lg+.form-control-feedback, .input-lg+.form-control-feedback {
	width: 46px;
	height: 46px;
	line-height: 46px
}
.form-group-sm .form-control+.form-control-feedback, .input-group-sm+.form-control-feedback, .input-sm+.form-control-feedback {
	width: 30px;
	height: 30px;
	line-height: 30px
}
.has-success .checkbox, .has-success .checkbox-inline, .has-success .control-label, .has-success .help-block, .has-success .radio, .has-success .radio-inline, .has-success.checkbox label, .has-success.checkbox-inline label, .has-success.radio label, .has-success.radio-inline label {
	color: #3c763d
}
.has-success .form-control {
	border-color: #3c763d;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}
.has-success .form-control:focus {
	border-color: #2b542c;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #67b168
}
.has-success .input-group-addon {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #3c763d
}
.has-success .form-control-feedback {
	color: #3c763d
}
.has-warning .checkbox, .has-warning .checkbox-inline, .has-warning .control-label, .has-warning .help-block, .has-warning .radio, .has-warning .radio-inline, .has-warning.checkbox label, .has-warning.checkbox-inline label, .has-warning.radio label, .has-warning.radio-inline label {
	color: #8a6d3b
}
.has-warning .form-control {
	border-color: #8a6d3b;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}
.has-warning .form-control:focus {
	border-color: #66512c;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #c0a16b
}
.has-warning .input-group-addon {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #8a6d3b
}
.has-warning .form-control-feedback {
	color: #8a6d3b
}
.has-error .checkbox, .has-error .checkbox-inline, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline, .has-error.checkbox label, .has-error.checkbox-inline label, .has-error.radio label, .has-error.radio-inline label {
	color: #a94442
}
.has-error .form-control {
	border-color: #a94442;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}
.has-error .form-control:focus {
	border-color: #843534;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #ce8483
}
.has-error .input-group-addon {
	color: #a94442;
	background-color: #f2dede;
	border-color: #a94442
}
.has-error .form-control-feedback {
	color: #a94442
}
.has-feedback label~.form-control-feedback {
	top: 25px
}
.has-feedback label.sr-only~.form-control-feedback {
	top: 0
}
.help-block {
	display: block;
	margin-top: 5px;
	margin-bottom: 10px;
	color: #737373
}
@media (min-width:768px) {
.form-inline .form-group {
	display: inline-block;
	margin-bottom: 0;
	vertical-align: middle
}
.form-inline .form-control {
	display: inline-block;
	width: auto;
	vertical-align: middle
}
.form-inline .form-control-static {
	display: inline-block
}
.form-inline .input-group {
	display: inline-table;
	vertical-align: middle
}
.form-inline .input-group .form-control, .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn {
	width: auto
}
.form-inline .input-group>.form-control {
	width: 100%
}
.form-inline .control-label {
	margin-bottom: 0;
	vertical-align: middle
}
.form-inline .checkbox, .form-inline .radio {
	display: inline-block;
	margin-top: 0;
	margin-bottom: 0;
	vertical-align: middle
}
.form-inline .checkbox label, .form-inline .radio label {
	padding-left: 0
}
.form-inline .checkbox input[type=checkbox], .form-inline .radio input[type=radio] {
	position: relative;
	margin-left: 0
}
.form-inline .has-feedback .form-control-feedback {
	top: 0
}
}
.form-horizontal .checkbox, .form-horizontal .checkbox-inline, .form-horizontal .radio, .form-horizontal .radio-inline {
	padding-top: 7px;
	margin-top: 0;
	margin-bottom: 0
}
.form-horizontal .checkbox, .form-horizontal .radio {
	min-height: 27px
}
.form-horizontal .form-group {
	margin-right: -15px;
	margin-left: -15px
}
@media (min-width:768px) {
.form-horizontal .control-label {
	padding-top: 7px;
	margin-bottom: 0;
	text-align: right
}
}
.form-horizontal .has-feedback .form-control-feedback {
	right: 15px
}
@media (min-width:768px) {
.form-horizontal .form-group-lg .control-label {
	padding-top: 11px;
	font-size: 18px
}
}
@media (min-width:768px) {
.form-horizontal .form-group-sm .control-label {
	padding-top: 6px;
	font-size: 12px
}
}
.btn {
	display: inline-block;
	padding: 6px 12px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px
}
.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px
}
.btn.focus, .btn:focus, .btn:hover {
	color: #333;
	text-decoration: none
}
.btn.active, .btn:active {
	background-image: none;
	outline: 0;
	box-shadow: inset 0 3px 5px rgba(0,0,0,.125)
}
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
	cursor: not-allowed;
	filter: alpha(opacity=65);
	box-shadow: none;
	opacity: .65
}
a.btn.disabled, fieldset[disabled] a.btn {
	pointer-events: none
}
.btn-default {
	color: #333;
	background-color: #fff;
	border-color: #ccc
}
.btn-default.focus, .btn-default:focus {
	color: #333;
	background-color: #e6e6e6;
	border-color: #8c8c8c
}
.btn-default.active, .btn-default:active, .btn-default:hover, .open>.dropdown-toggle.btn-default {
	color: #333;
	background-color: #e6e6e6;
	border-color: #adadad
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
	color: #333;
	background-color: #d4d4d4;
	border-color: #8c8c8c
}
.btn-default.active, .btn-default:active, .open>.dropdown-toggle.btn-default {
	background-image: none
}
.btn-default.disabled.focus, .btn-default.disabled:focus, .btn-default.disabled:hover, .btn-default[disabled].focus, .btn-default[disabled]:focus, .btn-default[disabled]:hover, fieldset[disabled] .btn-default.focus, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default:hover {
	background-color: #fff;
	border-color: #ccc
}
.btn-default .badge {
	color: #fff;
	background-color: #333
}
.btn-primary {
	color: #fff;
	background-color: #337ab7;
	border-color: #2e6da4
}
.btn-primary.focus, .btn-primary:focus {
	color: #fff;
	background-color: #286090;
	border-color: #122b40
}
.btn-primary.active, .btn-primary:active, .btn-primary:hover, .open>.dropdown-toggle.btn-primary {
	color: #fff;
	background-color: #286090;
	border-color: #204d74
}
.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover, .btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover, .open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus, .open>.dropdown-toggle.btn-primary:hover {
	color: #fff;
	background-color: #204d74;
	border-color: #122b40
}
.btn-primary.active, .btn-primary:active, .open>.dropdown-toggle.btn-primary {
	background-image: none
}
.btn-primary.disabled.focus, .btn-primary.disabled:focus, .btn-primary.disabled:hover, .btn-primary[disabled].focus, .btn-primary[disabled]:focus, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary.focus, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary:hover {
	background-color: #337ab7;
	border-color: #2e6da4
}
.btn-primary .badge {
	color: #337ab7;
	background-color: #fff
}
.btn-success {
	color: #fff;
	background-color: #5cb85c;
	border-color: #4cae4c
}
.btn-success.focus, .btn-success:focus {
	color: #fff;
	background-color: #449d44;
	border-color: #255625
}
.btn-success.active, .btn-success:active, .btn-success:hover, .open>.dropdown-toggle.btn-success {
	color: #fff;
	background-color: #449d44;
	border-color: #398439
}
.btn-success.active.focus, .btn-success.active:focus, .btn-success.active:hover, .btn-success:active.focus, .btn-success:active:focus, .btn-success:active:hover, .open>.dropdown-toggle.btn-success.focus, .open>.dropdown-toggle.btn-success:focus, .open>.dropdown-toggle.btn-success:hover {
	color: #fff;
	background-color: #398439;
	border-color: #255625
}
.btn-success.active, .btn-success:active, .open>.dropdown-toggle.btn-success {
	background-image: none
}
.btn-success.disabled.focus, .btn-success.disabled:focus, .btn-success.disabled:hover, .btn-success[disabled].focus, .btn-success[disabled]:focus, .btn-success[disabled]:hover, fieldset[disabled] .btn-success.focus, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success:hover {
	background-color: #5cb85c;
	border-color: #4cae4c
}
.btn-success .badge {
	color: #5cb85c;
	background-color: #fff
}
.btn-info {
	color: #fff;
	background-color: #5bc0de;
	border-color: #46b8da
}
.btn-info.focus, .btn-info:focus {
	color: #fff;
	background-color: #31b0d5;
	border-color: #1b6d85
}
.btn-info.active, .btn-info:active, .btn-info:hover, .open>.dropdown-toggle.btn-info {
	color: #fff;
	background-color: #31b0d5;
	border-color: #269abc
}
.btn-info.active.focus, .btn-info.active:focus, .btn-info.active:hover, .btn-info:active.focus, .btn-info:active:focus, .btn-info:active:hover, .open>.dropdown-toggle.btn-info.focus, .open>.dropdown-toggle.btn-info:focus, .open>.dropdown-toggle.btn-info:hover {
	color: #fff;
	background-color: #269abc;
	border-color: #1b6d85
}
.btn-info.active, .btn-info:active, .open>.dropdown-toggle.btn-info {
	background-image: none
}
.btn-info.disabled.focus, .btn-info.disabled:focus, .btn-info.disabled:hover, .btn-info[disabled].focus, .btn-info[disabled]:focus, .btn-info[disabled]:hover, fieldset[disabled] .btn-info.focus, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info:hover {
	background-color: #5bc0de;
	border-color: #46b8da
}
.btn-info .badge {
	color: #5bc0de;
	background-color: #fff
}
.btn-warning {
	color: #fff;
	background-color: #f0ad4e;
	border-color: #eea236
}
.btn-warning.focus, .btn-warning:focus {
	color: #fff;
	background-color: #ec971f;
	border-color: #985f0d
}
.btn-warning.active, .btn-warning:active, .btn-warning:hover, .open>.dropdown-toggle.btn-warning {
	color: #fff;
	background-color: #ec971f;
	border-color: #d58512
}
.btn-warning.active.focus, .btn-warning.active:focus, .btn-warning.active:hover, .btn-warning:active.focus, .btn-warning:active:focus, .btn-warning:active:hover, .open>.dropdown-toggle.btn-warning.focus, .open>.dropdown-toggle.btn-warning:focus, .open>.dropdown-toggle.btn-warning:hover {
	color: #fff;
	background-color: #d58512;
	border-color: #985f0d
}
.btn-warning.active, .btn-warning:active, .open>.dropdown-toggle.btn-warning {
	background-image: none
}
.btn-warning.disabled.focus, .btn-warning.disabled:focus, .btn-warning.disabled:hover, .btn-warning[disabled].focus, .btn-warning[disabled]:focus, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning.focus, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning:hover {
	background-color: #f0ad4e;
	border-color: #eea236
}
.btn-warning .badge {
	color: #f0ad4e;
	background-color: #fff
}
.btn-danger {
	color: #fff;
	background-color: #d9534f;
	border-color: #d43f3a
}
.btn-danger.focus, .btn-danger:focus {
	color: #fff;
	background-color: #c9302c;
	border-color: #761c19
}
.btn-danger.active, .btn-danger:active, .btn-danger:hover, .open>.dropdown-toggle.btn-danger {
	color: #fff;
	background-color: #c9302c;
	border-color: #ac2925
}
.btn-danger.active.focus, .btn-danger.active:focus, .btn-danger.active:hover, .btn-danger:active.focus, .btn-danger:active:focus, .btn-danger:active:hover, .open>.dropdown-toggle.btn-danger.focus, .open>.dropdown-toggle.btn-danger:focus, .open>.dropdown-toggle.btn-danger:hover {
	color: #fff;
	background-color: #ac2925;
	border-color: #761c19
}
.btn-danger.active, .btn-danger:active, .open>.dropdown-toggle.btn-danger {
	background-image: none
}
.btn-danger.disabled.focus, .btn-danger.disabled:focus, .btn-danger.disabled:hover, .btn-danger[disabled].focus, .btn-danger[disabled]:focus, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger.focus, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger:hover {
	background-color: #d9534f;
	border-color: #d43f3a
}
.btn-danger .badge {
	color: #d9534f;
	background-color: #fff
}
.btn-link {
	font-weight: 400;
	color: #337ab7;
	border-radius: 0
}
.btn-link, .btn-link.active, .btn-link:active, .btn-link[disabled], fieldset[disabled] .btn-link {
	background-color: transparent;
	box-shadow: none
}
.btn-link, .btn-link:active, .btn-link:focus, .btn-link:hover {
	border-color: transparent
}
.btn-link:focus, .btn-link:hover {
	color: #23527c;
	text-decoration: underline;
	background-color: transparent
}
.btn-link[disabled]:focus, .btn-link[disabled]:hover, fieldset[disabled] .btn-link:focus, fieldset[disabled] .btn-link:hover {
	color: #777;
	text-decoration: none
}
.btn-group-lg>.btn, .btn-lg {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}
.btn-group-sm>.btn, .btn-sm {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}
.btn-group-xs>.btn, .btn-xs {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}
.btn-block {
	display: block;
	width: 100%
}
.btn-block+.btn-block {
	margin-top: 5px
}
input[type=button].btn-block, input[type=reset].btn-block, input[type=submit].btn-block {
	width: 100%
}
.fade {
	opacity: 0;
	transition: opacity .15s linear
}
.fade.in {
	opacity: 1
}
.collapse {
	display: none
}
.collapse.in {
	display: block
}
tr.collapse.in {
	display: table-row
}
tbody.collapse.in {
	display: table-row-group
}
.collapsing {
	position: relative;
	height: 0;
	overflow: hidden;
	transition-timing-function: ease;
	transition-duration: .35s;
	transition-property: height, visibility
}
.caret {
	display: inline-block;
	width: 0;
	height: 0;
	margin-left: 2px;
	vertical-align: middle;
	border-top: 4px dashed;
	border-top: 4px solid\9;
	border-right: 4px solid transparent;
	border-left: 4px solid transparent
}
.dropdown, .dropup {
	position: relative
}
.dropdown-toggle:focus {
	outline: 0
}
.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 10;
	display: none;
	float: left;
	min-width: 160px;
	padding: 5px 0;
	margin: 2px 0 0;
	font-size: 14px;
	text-align: left;
	list-style: none;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #ccc;
	border: 1px solid rgba(0,0,0,.15);
	border-radius: 4px;
	box-shadow: 0 6px 12px rgba(0,0,0,.175)
}
.dropdown-menu.pull-right {
	right: 0;
	left: auto
}
.dropdown-menu .divider {
	height: 1px;
	margin: 9px 0;
	overflow: hidden;
	background-color: #e5e5e5
}
.dropdown-menu>li>a {
	display: block;
	padding: 3px 20px;
	clear: both;
	font-weight: 400;
	line-height: 1.42857143;
	color: #333;
	white-space: nowrap
}
.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
	color: #262626;
	text-decoration: none;
	background-color: #f5f5f5
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:focus, .dropdown-menu>.active>a:hover {
	color: #fff;
	text-decoration: none;
	background-color: #337ab7;
	outline: 0
}
.dropdown-menu>.disabled>a, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover {
	color: #777
}
.dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover {
	text-decoration: none;
	cursor: not-allowed;
	background-color: transparent;
	background-image: none;
filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)
}
.open>.dropdown-menu {
	display: block
}
.open>a {
	outline: 0
}
.dropdown-menu-right {
	right: 0;
	left: auto
}
.dropdown-menu-left {
	right: auto;
	left: 0
}
.dropdown-header {
	display: block;
	padding: 3px 20px;
	font-size: 12px;
	line-height: 1.42857143;
	color: #777;
	white-space: nowrap
}
.dropdown-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 8
}
.pull-right>.dropdown-menu {
	right: 0;
	left: auto
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
	content: "";
	border-top: 0;
	border-bottom: 4px dashed;
	border-bottom: 4px solid\9
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
	top: auto;
	bottom: 100%;
	margin-bottom: 2px
}
@media (min-width:768px) {
.navbar-right .dropdown-menu {
	right: 0;
	left: auto
}
.navbar-right .dropdown-menu-left {
	right: auto;
	left: 0
}
}
.btn-group, .btn-group-vertical {
	position: relative;
	display: inline-block;
	vertical-align: middle
}
.btn-group-vertical>.btn, .btn-group>.btn {
	position: relative;
	float: left
}
.btn-group-vertical>.btn.active, .btn-group-vertical>.btn:active, .btn-group-vertical>.btn:focus, .btn-group-vertical>.btn:hover, .btn-group>.btn.active, .btn-group>.btn:active, .btn-group>.btn:focus, .btn-group>.btn:hover {
	z-index: 1
}
.btn-group .btn+.btn, .btn-group .btn+.btn-group, .btn-group .btn-group+.btn, .btn-group .btn-group+.btn-group {
	margin-left: -1px
}
.btn-toolbar {
	margin-left: -5px
}
.btn-toolbar .btn, .btn-toolbar .btn-group, .btn-toolbar .input-group {
	float: left
}
.btn-toolbar>.btn, .btn-toolbar>.btn-group, .btn-toolbar>.input-group {
	margin-left: 5px
}
.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
	border-radius: 0
}
.btn-group>.btn:first-child {
	margin-left: 0
}
.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}
.btn-group>.btn:last-child:not(:first-child), .btn-group>.dropdown-toggle:not(:first-child) {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}
.btn-group>.btn-group {
	float: left
}
.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
	border-radius: 0
}
.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child, .btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}
.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}
.btn-group .dropdown-toggle:active, .btn-group.open .dropdown-toggle {
	outline: 0
}
.btn-group>.btn+.dropdown-toggle {
	padding-right: 8px;
	padding-left: 8px
}
.btn-group>.btn-lg+.dropdown-toggle {
	padding-right: 12px;
	padding-left: 12px
}
.btn-group.open .dropdown-toggle {
	box-shadow: inset 0 3px 5px rgba(0,0,0,.125)
}
.btn-group.open .dropdown-toggle.btn-link {
	box-shadow: none
}
.btn .caret {
	margin-left: 0
}
.btn-lg .caret {
	border-width: 5px 5px 0;
	border-bottom-width: 0
}
.dropup .btn-lg .caret {
	border-width: 0 5px 5px
}
.btn-group-vertical>.btn, .btn-group-vertical>.btn-group, .btn-group-vertical>.btn-group>.btn {
	display: block;
	float: none;
	width: 100%;
	max-width: 100%
}
.btn-group-vertical>.btn-group>.btn {
	float: none
}
.btn-group-vertical>.btn+.btn, .btn-group-vertical>.btn+.btn-group, .btn-group-vertical>.btn-group+.btn, .btn-group-vertical>.btn-group+.btn-group {
	margin-top: -1px;
	margin-left: 0
}
.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
	border-radius: 0
}
.btn-group-vertical>.btn:first-child:not(:last-child) {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}
.btn-group-vertical>.btn:last-child:not(:first-child) {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px
}
.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
	border-radius: 0
}
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child, .btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}
.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
	border-top-left-radius: 0;
	border-top-right-radius: 0
}
.btn-group-justified {
	display: table;
	width: 100%;
	table-layout: fixed;
	border-collapse: separate
}
.btn-group-justified>.btn, .btn-group-justified>.btn-group {
	display: table-cell;
	float: none;
	width: 1%
}
.btn-group-justified>.btn-group .btn {
	width: 100%
}
.btn-group-justified>.btn-group .dropdown-menu {
	left: auto
}
[data-toggle=buttons]>.btn input[type=checkbox], [data-toggle=buttons]>.btn input[type=radio], [data-toggle=buttons]>.btn-group>.btn input[type=checkbox], [data-toggle=buttons]>.btn-group>.btn input[type=radio] {
position:absolute;
clip:rect(0,0,0,0);
pointer-events:none
}
.input-group {
	position: relative;
	display: table;
	border-collapse: separate
}
.input-group[class*=col-] {
	float: none;
	padding-right: 0;
	padding-left: 0
}
.input-group .form-control {
	position: relative;
	z-index: 1;
	float: left;
	width: 100%;
	margin-bottom: 0
}
.input-group .form-control:focus {
	z-index: 2
}
.input-group-lg>.form-control, .input-group-lg>.input-group-addon, .input-group-lg>.input-group-btn>.btn {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}
select.input-group-lg>.form-control, select.input-group-lg>.input-group-addon, select.input-group-lg>.input-group-btn>.btn {
	height: 46px;
	line-height: 46px
}
select[multiple].input-group-lg>.form-control, select[multiple].input-group-lg>.input-group-addon, select[multiple].input-group-lg>.input-group-btn>.btn, textarea.input-group-lg>.form-control, textarea.input-group-lg>.input-group-addon, textarea.input-group-lg>.input-group-btn>.btn {
	height: auto
}
.input-group-sm>.form-control, .input-group-sm>.input-group-addon, .input-group-sm>.input-group-btn>.btn {
	height: 30px;
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px
}
select.input-group-sm>.form-control, select.input-group-sm>.input-group-addon, select.input-group-sm>.input-group-btn>.btn {
	height: 30px;
	line-height: 30px
}
select[multiple].input-group-sm>.form-control, select[multiple].input-group-sm>.input-group-addon, select[multiple].input-group-sm>.input-group-btn>.btn, textarea.input-group-sm>.form-control, textarea.input-group-sm>.input-group-addon, textarea.input-group-sm>.input-group-btn>.btn {
	height: auto
}
.input-group .form-control, .input-group-addon, .input-group-btn {
	display: table-cell
}
.input-group .form-control:not(:first-child):not(:last-child), .input-group-addon:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child) {
	border-radius: 0
}
.input-group-addon, .input-group-btn {
	width: 1%;
	white-space: nowrap;
	vertical-align: middle
}
.input-group-addon {
	padding: 6px 12px;
	font-size: 14px;
	font-weight: 400;
	line-height: 1;
	color: #555;
	text-align: center;
	background-color: #eee;
	border: 1px solid #ccc;
	border-radius: 4px
}
.input-group-addon.input-sm {
	padding: 5px 10px;
	font-size: 12px;
	border-radius: 3px
}
.input-group-addon.input-lg {
	padding: 10px 16px;
	font-size: 18px;
	border-radius: 6px
}
.input-group-addon input[type=checkbox], .input-group-addon input[type=radio] {
	margin-top: 0
}
.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child>.btn, .input-group-btn:first-child>.btn-group>.btn, .input-group-btn:first-child>.dropdown-toggle, .input-group-btn:last-child>.btn-group:not(:last-child)>.btn, .input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle) {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0
}
.input-group-addon:first-child {
	border-right: 0
}
.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:first-child>.btn-group:not(:first-child)>.btn, .input-group-btn:first-child>.btn:not(:first-child), .input-group-btn:last-child>.btn, .input-group-btn:last-child>.btn-group>.btn, .input-group-btn:last-child>.dropdown-toggle {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0
}
.input-group-addon:last-child {
	border-left: 0
}
.input-group-btn {
	font-size: 0;
	white-space: nowrap
}
.input-group-btn, .input-group-btn>.btn {
	position: relative
}
.input-group-btn>.btn+.btn {
	margin-left: -1px
}
.input-group-btn>.btn:active, .input-group-btn>.btn:focus, .input-group-btn>.btn:hover {
	z-index: 1
}
.input-group-btn:first-child>.btn, .input-group-btn:first-child>.btn-group {
	margin-right: -1px
}
.input-group-btn:last-child>.btn, .input-group-btn:last-child>.btn-group {
	z-index: 1;
	margin-left: -1px
}
.nav {
	padding-left: 0;
	margin-bottom: 0;
	list-style: none
}
.nav>li, .nav>li>a {
	position: relative;
	display: block
}
.nav>li>a {
	padding: 10px 15px
}
.nav>li>a:focus, .nav>li>a:hover {
	text-decoration: none;
	background-color: #eee
}
.nav>li.disabled>a {
	color: #777
}
.nav>li.disabled>a:focus, .nav>li.disabled>a:hover {
	color: #777;
	text-decoration: none;
	cursor: not-allowed;
	background-color: transparent
}
.nav .open>a, .nav .open>a:focus, .nav .open>a:hover {
	background-color: #eee;
	border-color: #337ab7
}
.nav .nav-divider {
	height: 1px;
	margin: 9px 0;
	overflow: hidden;
	background-color: #e5e5e5
}
.nav>li>a>img {
	max-width: none
}
.nav-tabs {
	border-bottom: 1px solid #ddd
}
.nav-tabs>li {
	float: left;
	margin-bottom: -1px
}
.nav-tabs>li>a {
	margin-right: 2px;
	line-height: 1.42857143;
	border: 1px solid transparent;
	border-radius: 4px 4px 0 0
}
.nav-tabs>li>a:hover {
	border-color: #eee #eee #ddd
}
.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
	color: #555;
	cursor: default;
	background-color: #fff;
	border: 1px solid #ddd;
	border-bottom-color: transparent
}
.nav-tabs.nav-justified {
	width: 100%;
	border-bottom: 0
}
.nav-tabs.nav-justified>li {
	float: none
}
.nav-tabs.nav-justified>li>a {
	margin-bottom: 5px;
	text-align: center
}
.nav-tabs.nav-justified>.dropdown .dropdown-menu {
	top: auto;
	left: auto
}
@media (min-width:768px) {
.nav-tabs.nav-justified>li {
	display: table-cell;
	width: 1%
}
.nav-tabs.nav-justified>li>a {
	margin-bottom: 0
}
}
.nav-tabs.nav-justified>li>a {
	margin-right: 0;
	border-radius: 4px
}
.nav-tabs.nav-justified>.active>a, .nav-tabs.nav-justified>.active>a:focus, .nav-tabs.nav-justified>.active>a:hover {
	border: 1px solid #ddd
}
@media (min-width:768px) {
.nav-tabs.nav-justified>li>a {
	border-bottom: 1px solid #ddd;
	border-radius: 4px 4px 0 0
}
.nav-tabs.nav-justified>.active>a, .nav-tabs.nav-justified>.active>a:focus, .nav-tabs.nav-justified>.active>a:hover {
	border-bottom-color: #fff
}
}
.nav-pills>li {
	float: left
}
.nav-pills>li>a {
	border-radius: 4px
}
.nav-pills>li+li {
	margin-left: 2px
}
.nav-pills>li.active>a, .nav-pills>li.active>a:focus, .nav-pills>li.active>a:hover {
	color: #fff;
	background-color: #337ab7
}
.nav-stacked>li {
	float: none
}
.nav-stacked>li+li {
	margin-top: 2px;
	margin-left: 0
}
.nav-justified {
	width: 100%
}
.nav-justified>li {
	float: none
}
.nav-justified>li>a {
	margin-bottom: 5px;
	text-align: center
}
.nav-justified>.dropdown .dropdown-menu {
	top: auto;
	left: auto
}
@media (min-width:768px) {
.nav-justified>li {
	display: table-cell;
	width: 1%
}
.nav-justified>li>a {
	margin-bottom: 0
}
}
.nav-tabs-justified {
	border-bottom: 0
}
.nav-tabs-justified>li>a {
	margin-right: 0;
	border-radius: 4px
}
.nav-tabs-justified>.active>a, .nav-tabs-justified>.active>a:focus, .nav-tabs-justified>.active>a:hover {
	border: 1px solid #ddd
}
@media (min-width:768px) {
.nav-tabs-justified>li>a {
	border-bottom: 1px solid #ddd;
	border-radius: 4px 4px 0 0
}
.nav-tabs-justified>.active>a, .nav-tabs-justified>.active>a:focus, .nav-tabs-justified>.active>a:hover {
	border-bottom-color: #fff
}
}
.tab-content>.tab-pane {
	display: none
}
.tab-content>.active {
	display: block
}
.nav-tabs .dropdown-menu {
	margin-top: -1px;
	border-top-left-radius: 0;
	border-top-right-radius: 0
}
.navbar {
	position: relative;
	min-height: 50px;
	margin-bottom: 20px;
	border: 1px solid transparent
}
@media (min-width:768px) {
.navbar {
	border-radius: 4px
}
}
@media (min-width:768px) {
.navbar-header {
	float: left
}
}
.navbar-collapse {
	padding-right: 15px;
	padding-left: 15px;
	overflow-x: visible;
	-webkit-overflow-scrolling: touch;
	border-top: 1px solid transparent;
	box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1)
}
.navbar-collapse.in {
	overflow-y: auto
}
@media (min-width:768px) {
.navbar-collapse {
	width: auto;
	border-top: 0;
	box-shadow: none
}
.navbar-collapse.collapse {
	display: block!important;
	height: auto!important;
	padding-bottom: 0;
	overflow: visible!important
}
.navbar-collapse.in {
	overflow-y: visible
}
.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse {
	padding-right: 0;
	padding-left: 0
}
}
.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
	max-height: 340px
}
@media (max-device-width:480px) and (orientation:landscape) {
.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
	max-height: 200px
}
}
.container-fluid>.navbar-collapse, .container-fluid>.navbar-header, .container>.navbar-collapse, .container>.navbar-header {
	margin-right: -15px;
	margin-left: -15px
}
@media (min-width:768px) {
.container-fluid>.navbar-collapse, .container-fluid>.navbar-header, .container>.navbar-collapse, .container>.navbar-header {
	margin-right: 0;
	margin-left: 0
}
}
.navbar-static-top {
	z-index: 10;
	border-width: 0 0 1px
}
@media (min-width:768px) {
.navbar-static-top {
	border-radius: 0
}
}
.navbar-fixed-bottom, .navbar-fixed-top {
	position: fixed;
	right: 0;
	left: 0;
	z-index: 11
}
@media (min-width:768px) {
.navbar-fixed-bottom, .navbar-fixed-top {
	border-radius: 0
}
}
.navbar-fixed-top {
	top: 0;
	border-width: 0 0 1px
}
.navbar-fixed-bottom {
	bottom: 0;
	margin-bottom: 0;
	border-width: 1px 0 0
}
.navbar-brand {
	float: left;
	height: 50px;
	padding: 15px;
	font-size: 18px;
	line-height: 20px
}
.navbar-brand:focus, .navbar-brand:hover {
	text-decoration: none
}
.navbar-brand>img {
	display: block
}
@media (min-width:768px) {
.navbar>.container .navbar-brand, .navbar>.container-fluid .navbar-brand {
	margin-left: -15px
}
}
.navbar-toggle {
	position: relative;
	float: right;
	padding: 9px 10px;
	margin-top: 8px;
	margin-right: 15px;
	margin-bottom: 8px;
	background-color: transparent;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px
}
.navbar-toggle:focus {
	outline: 0
}
.navbar-toggle .icon-bar {
	display: block;
	width: 22px;
	height: 2px;
	border-radius: 1px
}
.navbar-toggle .icon-bar+.icon-bar {
	margin-top: 4px
}
@media (min-width:768px) {
.navbar-toggle {
	display: none
}
}
.navbar-nav {
	margin: 7.5px -15px
}
.navbar-nav>li>a {
	padding-top: 10px;
	padding-bottom: 10px;
	line-height: 20px
}
@media (max-width:767px) {
.navbar-nav .open .dropdown-menu {
	position: static;
	float: none;
	width: auto;
	margin-top: 0;
	background-color: transparent;
	border: 0;
	box-shadow: none
}
.navbar-nav .open .dropdown-menu .dropdown-header, .navbar-nav .open .dropdown-menu>li>a {
	padding: 5px 15px 5px 25px
}
.navbar-nav .open .dropdown-menu>li>a {
	line-height: 20px
}
.navbar-nav .open .dropdown-menu>li>a:focus, .navbar-nav .open .dropdown-menu>li>a:hover {
	background-image: none
}
}
@media (min-width:768px) {
.navbar-nav {
	float: left;
	margin: 0
}
.navbar-nav>li {
	float: left
}
.navbar-nav>li>a {
	padding-top: 15px;
	padding-bottom: 15px
}
}
.navbar-form {
	padding: 10px 15px;
	margin: 8px -15px;
	border-top: 1px solid transparent;
	border-bottom: 1px solid transparent;
	box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1), 0 1px 0 hsla(0,0%,100%,.1)
}
@media (min-width:768px) {
.navbar-form .form-group {
	display: inline-block;
	margin-bottom: 0;
	vertical-align: middle
}
.navbar-form .form-control {
	display: inline-block;
	width: auto;
	vertical-align: middle
}
.navbar-form .form-control-static {
	display: inline-block
}
.navbar-form .input-group {
	display: inline-table;
	vertical-align: middle
}
.navbar-form .input-group .form-control, .navbar-form .input-group .input-group-addon, .navbar-form .input-group .input-group-btn {
	width: auto
}
.navbar-form .input-group>.form-control {
	width: 100%
}
.navbar-form .control-label {
	margin-bottom: 0;
	vertical-align: middle
}
.navbar-form .checkbox, .navbar-form .radio {
	display: inline-block;
	margin-top: 0;
	margin-bottom: 0;
	vertical-align: middle
}
.navbar-form .checkbox label, .navbar-form .radio label {
	padding-left: 0
}
.navbar-form .checkbox input[type=checkbox], .navbar-form .radio input[type=radio] {
	position: relative;
	margin-left: 0
}
.navbar-form .has-feedback .form-control-feedback {
	top: 0
}
}
@media (max-width:767px) {
.navbar-form .form-group {
	margin-bottom: 5px
}
.navbar-form .form-group:last-child {
	margin-bottom: 0
}
}
@media (min-width:768px) {
.navbar-form {
	width: auto;
	padding-top: 0;
	padding-bottom: 0;
	margin-right: 0;
	margin-left: 0;
	border: 0;
	box-shadow: none
}
}
.navbar-nav>li>.dropdown-menu {
	margin-top: 0;
	border-top-left-radius: 0;
	border-top-right-radius: 0
}
.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
	margin-bottom: 0;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0
}
.navbar-btn {
	margin-top: 8px;
	margin-bottom: 8px
}
.navbar-btn.btn-sm {
	margin-top: 10px;
	margin-bottom: 10px
}
.navbar-btn.btn-xs {
	margin-top: 14px;
	margin-bottom: 14px
}
.navbar-text {
	margin-top: 15px;
	margin-bottom: 15px
}
@media (min-width:768px) {
.navbar-text {
	float: left;
	margin-right: 15px;
	margin-left: 15px
}
}
@media (min-width:768px) {
.navbar-left {
	float: left!important
}
.navbar-right {
	float: right!important;
	margin-right: -15px
}
.navbar-right~.navbar-right {
	margin-right: 0
}
}
.navbar-default {
	background-color: #f8f8f8;
	border-color: #e7e7e7
}
.navbar-default .navbar-brand {
	color: #777
}
.navbar-default .navbar-brand:focus, .navbar-default .navbar-brand:hover {
	color: #5e5e5e;
	background-color: transparent
}
.navbar-default .navbar-nav>li>a, .navbar-default .navbar-text {
	color: #777
}
.navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
	color: #333;
	background-color: transparent
}
.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover {
	color: #555;
	background-color: #e7e7e7
}
.navbar-default .navbar-nav>.disabled>a, .navbar-default .navbar-nav>.disabled>a:focus, .navbar-default .navbar-nav>.disabled>a:hover {
	color: #ccc;
	background-color: transparent
}
.navbar-default .navbar-toggle {
	border-color: #ddd
}
.navbar-default .navbar-toggle:focus, .navbar-default .navbar-toggle:hover {
	background-color: #ddd
}
.navbar-default .navbar-toggle .icon-bar {
	background-color: #888
}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
	border-color: #e7e7e7
}
.navbar-default .navbar-nav>.open>a, .navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover {
	color: #555;
	background-color: #e7e7e7
}
@media (max-width:767px) {
.navbar-default .navbar-nav .open .dropdown-menu>li>a {
	color: #777
}
.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
	color: #333;
	background-color: transparent
}
.navbar-default .navbar-nav .open .dropdown-menu>.active>a, .navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover {
	color: #555;
	background-color: #e7e7e7
}
.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a, .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover {
	color: #ccc;
	background-color: transparent
}
}
.navbar-default .navbar-link {
	color: #777
}
.navbar-default .navbar-link:hover {
	color: #333
}
.navbar-default .btn-link {
	color: #777
}
.navbar-default .btn-link:focus, .navbar-default .btn-link:hover {
	color: #333
}
.navbar-default .btn-link[disabled]:focus, .navbar-default .btn-link[disabled]:hover, fieldset[disabled] .navbar-default .btn-link:focus, fieldset[disabled] .navbar-default .btn-link:hover {
	color: #ccc
}
.navbar-inverse {
	background-color: #222;
	border-color: #080808
}
.navbar-inverse .navbar-brand {
	color: #9d9d9d
}
.navbar-inverse .navbar-brand:focus, .navbar-inverse .navbar-brand:hover {
	color: #fff;
	background-color: transparent
}
.navbar-inverse .navbar-nav>li>a, .navbar-inverse .navbar-text {
	color: #9d9d9d
}
.navbar-inverse .navbar-nav>li>a:focus, .navbar-inverse .navbar-nav>li>a:hover {
	color: #fff;
	background-color: transparent
}
.navbar-inverse .navbar-nav>.active>a, .navbar-inverse .navbar-nav>.active>a:focus, .navbar-inverse .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: #080808
}
.navbar-inverse .navbar-nav>.disabled>a, .navbar-inverse .navbar-nav>.disabled>a:focus, .navbar-inverse .navbar-nav>.disabled>a:hover {
	color: #444;
	background-color: transparent
}
.navbar-inverse .navbar-toggle {
	border-color: #333
}
.navbar-inverse .navbar-toggle:focus, .navbar-inverse .navbar-toggle:hover {
	background-color: #333
}
.navbar-inverse .navbar-toggle .icon-bar {
	background-color: #fff
}
.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
	border-color: #101010
}
.navbar-inverse .navbar-nav>.open>a, .navbar-inverse .navbar-nav>.open>a:focus, .navbar-inverse .navbar-nav>.open>a:hover {
	color: #fff;
	background-color: #080808
}
@media (max-width:767px) {
.navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
	border-color: #080808
}
.navbar-inverse .navbar-nav .open .dropdown-menu .divider {
	background-color: #080808
}
.navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
	color: #9d9d9d
}
.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover {
	color: #fff;
	background-color: transparent
}
.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a, .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover {
	color: #fff;
	background-color: #080808
}
.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a, .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover {
	color: #444;
	background-color: transparent
}
}
.navbar-inverse .navbar-link {
	color: #9d9d9d
}
.navbar-inverse .navbar-link:hover {
	color: #fff
}
.navbar-inverse .btn-link {
	color: #9d9d9d
}
.navbar-inverse .btn-link:focus, .navbar-inverse .btn-link:hover {
	color: #fff
}
.navbar-inverse .btn-link[disabled]:focus, .navbar-inverse .btn-link[disabled]:hover, fieldset[disabled] .navbar-inverse .btn-link:focus, fieldset[disabled] .navbar-inverse .btn-link:hover {
	color: #444
}
.breadcrumb {
	padding: 8px 15px;
	margin-bottom: 20px;
	list-style: none;
	background-color: #f5f5f5;
	border-radius: 4px
}
.breadcrumb>li {
	display: inline-block
}
.breadcrumb>li+li:before {
	padding: 0 5px;
	color: #ccc;
	content: "/\00a0"
}
.breadcrumb>.active {
	color: #777
}
.pagination {
	display: inline-block;
	padding-left: 0;
	margin: 20px 0;
	border-radius: 4px
}
.pagination>li {
	display: inline
}
.pagination>li>a, .pagination>li>span {
	position: relative;
	float: left;
	padding: 6px 12px;
	margin-left: -1px;
	line-height: 1.42857143;
	color: #337ab7;
	text-decoration: none;
	background-color: #fff;
	border: 1px solid #ddd
}
.pagination>li:first-child>a, .pagination>li:first-child>span {
	margin-left: 0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px
}
.pagination>li:last-child>a, .pagination>li:last-child>span {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px
}
.pagination>li>a:focus, .pagination>li>a:hover, .pagination>li>span:focus, .pagination>li>span:hover {
	z-index: 1;
	color: #23527c;
	background-color: #eee;
	border-color: #ddd
}
.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover {
	z-index: 2;
	color: #fff;
	cursor: default;
	background-color: #337ab7;
	border-color: #337ab7
}
.pagination>.disabled>a, .pagination>.disabled>a:focus, .pagination>.disabled>a:hover, .pagination>.disabled>span, .pagination>.disabled>span:focus, .pagination>.disabled>span:hover {
	color: #777;
	cursor: not-allowed;
	background-color: #fff;
	border-color: #ddd
}
.pagination-lg>li>a, .pagination-lg>li>span {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333
}
.pagination-lg>li:first-child>a, .pagination-lg>li:first-child>span {
	border-top-left-radius: 6px;
	border-bottom-left-radius: 6px
}
.pagination-lg>li:last-child>a, .pagination-lg>li:last-child>span {
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px
}
.pagination-sm>li>a, .pagination-sm>li>span {
	padding: 5px 10px;
	font-size: 12px;
	line-height: 1.5
}
.pagination-sm>li:first-child>a, .pagination-sm>li:first-child>span {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px
}
.pagination-sm>li:last-child>a, .pagination-sm>li:last-child>span {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px
}
.pager {
	padding-left: 0;
	margin: 20px 0;
	text-align: center;
	list-style: none
}
.pager li {
	display: inline
}
.pager li>a, .pager li>span {
	display: inline-block;
	padding: 5px 14px;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 15px
}
.pager li>a:focus, .pager li>a:hover {
	text-decoration: none;
	background-color: #eee
}
.pager .next>a, .pager .next>span {
	float: right
}
.pager .previous>a, .pager .previous>span {
	float: left
}
.pager .disabled>a, .pager .disabled>a:focus, .pager .disabled>a:hover, .pager .disabled>span {
	color: #777;
	cursor: not-allowed;
	background-color: #fff
}
.label {
	display: inline;
	padding: .2em .6em .3em;
	font-size: 75%;
	font-weight: 700;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: .25em
}
a.label:focus, a.label:hover {
	color: #fff;
	text-decoration: none;
	cursor: pointer
}
.label:empty {
	display: none
}
.btn .label {
	position: relative;
	top: -1px
}
.label-default {
	background-color: #777
}
.label-default[href]:focus, .label-default[href]:hover {
	background-color: #5e5e5e
}
.label-primary {
	background-color: #337ab7
}
.label-primary[href]:focus, .label-primary[href]:hover {
	background-color: #286090
}
.label-success {
	background-color: #5cb85c
}
.label-success[href]:focus, .label-success[href]:hover {
	background-color: #449d44
}
.label-info {
	background-color: #5bc0de
}
.label-info[href]:focus, .label-info[href]:hover {
	background-color: #31b0d5
}
.label-warning {
	background-color: #f0ad4e
}
.label-warning[href]:focus, .label-warning[href]:hover {
	background-color: #ec971f
}
.label-danger {
	background-color: #d9534f
}
.label-danger[href]:focus, .label-danger[href]:hover {
	background-color: #c9302c
}
.badge {
	display: inline-block;
	min-width: 10px;
	padding: 3px 7px;
	font-size: 12px;
	font-weight: 700;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	background-color: #777;
	border-radius: 10px
}
.badge:empty {
	display: none
}
.btn .badge {
	position: relative;
	top: -1px
}
.btn-group-xs>.btn .badge, .btn-xs .badge {
	top: 0;
	padding: 1px 5px
}
a.badge:focus, a.badge:hover {
	color: #fff;
	text-decoration: none;
	cursor: pointer
}
.list-group-item.active>.badge, .nav-pills>.active>a>.badge {
	color: #337ab7;
	background-color: #fff
}
.list-group-item>.badge {
	float: right
}
.list-group-item>.badge+.badge {
	margin-right: 5px
}
.nav-pills>li>a>.badge {
	margin-left: 3px
}
.jumbotron {
	padding-top: 30px;
	padding-bottom: 30px;
	margin-bottom: 30px;
	background-color: #eee
}
.jumbotron, .jumbotron .h1, .jumbotron h1 {
	color: inherit
}
.jumbotron p {
	margin-bottom: 15px;
	font-size: 21px;
	font-weight: 200
}
.jumbotron>hr {
	border-top-color: #d5d5d5
}
.container .jumbotron, .container-fluid .jumbotron {
	padding-right: 15px;
	padding-left: 15px;
	border-radius: 6px
}
.jumbotron .container {
	max-width: 100%
}
@media screen and (min-width:768px) {
.jumbotron {
	padding-top: 48px;
	padding-bottom: 48px
}
.container .jumbotron, .container-fluid .jumbotron {
	padding-right: 60px;
	padding-left: 60px
}
.jumbotron .h1, .jumbotron h1 {
	font-size: 63px
}
}
.thumbnail {
	display: block;
	padding: 4px;
	margin-bottom: 20px;
	line-height: 1.42857143;
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
	transition: border .2s ease-in-out
}
.thumbnail a>img, .thumbnail>img {
	margin-right: auto;
	margin-left: auto
}
a.thumbnail.active, a.thumbnail:focus, a.thumbnail:hover {
	border-color: #337ab7
}
.thumbnail .caption {
	padding: 9px;
	color: #333
}
.alert {
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px
}
.alert h4 {
	margin-top: 0;
	color: inherit
}
.alert .alert-link {
	font-weight: 700
}
.alert>p, .alert>ul {
	margin-bottom: 0
}
.alert>p+p {
	margin-top: 5px
}
.alert-dismissable, .alert-dismissible {
	padding-right: 35px
}
.alert-dismissable .close, .alert-dismissible .close {
	position: relative;
	top: -2px;
	right: -21px;
	color: inherit
}
.alert-success {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6
}
.alert-success hr {
	border-top-color: #c9e2b3
}
.alert-success .alert-link {
	color: #2b542c
}
.alert-info {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1
}
.alert-info hr {
	border-top-color: #a6e1ec
}
.alert-info .alert-link {
	color: #245269
}
.alert-warning {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc
}
.alert-warning hr {
	border-top-color: #f7e1b5
}
.alert-warning .alert-link {
	color: #66512c
}
.alert-danger {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1
}
.alert-danger hr {
	border-top-color: #e4b9c0
}
.alert-danger .alert-link {
	color: #843534
}
@-webkit-keyframes a {
0% {
background-position:40px 0
}
to {
	background-position: 0 0
}
}
@keyframes a {
0% {
background-position:40px 0
}
to {
	background-position: 0 0
}
}
.progress {
	height: 20px;
	margin-bottom: 20px;
	overflow: hidden;
	background-color: #f5f5f5;
	border-radius: 4px;
	box-shadow: inset 0 1px 2px rgba(0,0,0,.1)
}
.progress-bar {
	float: left;
	width: 0;
	height: 100%;
	font-size: 12px;
	line-height: 20px;
	color: #fff;
	text-align: center;
	background-color: #337ab7;
	box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
	transition: width .6s ease
}
.progress-bar-striped, .progress-striped .progress-bar {
	background-image: linear-gradient(45deg, hsla(0,0%,100%,.15) 25%, transparent 25%, transparent 50%, hsla(0,0%,100%,.15) 50%, hsla(0,0%,100%,.15) 75%, transparent 75%, transparent);
	background-size: 40px 40px
}
.progress-bar.active, .progress.active .progress-bar {
	-webkit-animation: a 2s linear infinite;
	animation: a 2s linear infinite
}
.progress-bar-success {
	background-color: #5cb85c
}
.progress-striped .progress-bar-success {
	background-image: linear-gradient(45deg, hsla(0,0%,100%,.15) 25%, transparent 25%, transparent 50%, hsla(0,0%,100%,.15) 50%, hsla(0,0%,100%,.15) 75%, transparent 75%, transparent)
}
.progress-bar-info {
	background-color: #5bc0de
}
.progress-striped .progress-bar-info {
	background-image: linear-gradient(45deg, hsla(0,0%,100%,.15) 25%, transparent 25%, transparent 50%, hsla(0,0%,100%,.15) 50%, hsla(0,0%,100%,.15) 75%, transparent 75%, transparent)
}
.progress-bar-warning {
	background-color: #f0ad4e
}
.progress-striped .progress-bar-warning {
	background-image: linear-gradient(45deg, hsla(0,0%,100%,.15) 25%, transparent 25%, transparent 50%, hsla(0,0%,100%,.15) 50%, hsla(0,0%,100%,.15) 75%, transparent 75%, transparent)
}
.progress-bar-danger {
	background-color: #d9534f
}
.progress-striped .progress-bar-danger {
	background-image: linear-gradient(45deg, hsla(0,0%,100%,.15) 25%, transparent 25%, transparent 50%, hsla(0,0%,100%,.15) 50%, hsla(0,0%,100%,.15) 75%, transparent 75%, transparent)
}
.media {
	margin-top: 15px
}
.media:first-child {
	margin-top: 0
}
.media, .media-body {
	overflow: hidden;
	zoom: 1
}
.media-body {
	width: 10000px
}
.media-object {
	display: block
}
.media-object.img-thumbnail {
	max-width: none
}
.media-right, .media>.pull-right {
	padding-left: 10px
}
.media-left, .media>.pull-left {
	padding-right: 10px
}
.media-body, .media-left, .media-right {
	display: table-cell;
	vertical-align: top
}
.media-middle {
	vertical-align: middle
}
.media-bottom {
	vertical-align: bottom
}
.media-heading {
	margin-top: 0;
	margin-bottom: 5px
}
.media-list {
	padding-left: 0;
	list-style: none
}
.list-group {
	padding-left: 0;
	margin-bottom: 20px
}
.list-group-item {
	position: relative;
	display: block;
	padding: 10px 15px;
	margin-bottom: -1px;
	background-color: #fff;
	border: 1px solid #ddd
}
.list-group-item:first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px
}
.list-group-item:last-child {
	margin-bottom: 0;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px
}
a.list-group-item, button.list-group-item {
	color: #555
}
a.list-group-item .list-group-item-heading, button.list-group-item .list-group-item-heading {
	color: #333
}
a.list-group-item:focus, a.list-group-item:hover, button.list-group-item:focus, button.list-group-item:hover {
	color: #555;
	text-decoration: none;
	background-color: #f5f5f5
}
button.list-group-item {
	width: 100%;
	text-align: left
}
.list-group-item.disabled, .list-group-item.disabled:focus, .list-group-item.disabled:hover {
	color: #777;
	cursor: not-allowed;
	background-color: #eee
}
.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading {
	color: inherit
}
.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text {
	color: #777
}
.list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
	z-index: 1;
	color: #fff;
	background-color: #337ab7;
	border-color: #337ab7
}
.list-group-item.active .list-group-item-heading, .list-group-item.active .list-group-item-heading>.small, .list-group-item.active .list-group-item-heading>small, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading>.small, .list-group-item.active:focus .list-group-item-heading>small, .list-group-item.active:hover .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading>.small, .list-group-item.active:hover .list-group-item-heading>small {
	color: inherit
}
.list-group-item.active .list-group-item-text, .list-group-item.active:focus .list-group-item-text, .list-group-item.active:hover .list-group-item-text {
	color: #c7ddef
}
.list-group-item-success {
	color: #3c763d;
	background-color: #dff0d8
}
a.list-group-item-success, button.list-group-item-success {
	color: #3c763d
}
a.list-group-item-success .list-group-item-heading, button.list-group-item-success .list-group-item-heading {
	color: inherit
}
a.list-group-item-success:focus, a.list-group-item-success:hover, button.list-group-item-success:focus, button.list-group-item-success:hover {
	color: #3c763d;
	background-color: #d0e9c6
}
a.list-group-item-success.active, a.list-group-item-success.active:focus, a.list-group-item-success.active:hover, button.list-group-item-success.active, button.list-group-item-success.active:focus, button.list-group-item-success.active:hover {
	color: #fff;
	background-color: #3c763d;
	border-color: #3c763d
}
.list-group-item-info {
	color: #31708f;
	background-color: #d9edf7
}
a.list-group-item-info, button.list-group-item-info {
	color: #31708f
}
a.list-group-item-info .list-group-item-heading, button.list-group-item-info .list-group-item-heading {
	color: inherit
}
a.list-group-item-info:focus, a.list-group-item-info:hover, button.list-group-item-info:focus, button.list-group-item-info:hover {
	color: #31708f;
	background-color: #c4e3f3
}
a.list-group-item-info.active, a.list-group-item-info.active:focus, a.list-group-item-info.active:hover, button.list-group-item-info.active, button.list-group-item-info.active:focus, button.list-group-item-info.active:hover {
	color: #fff;
	background-color: #31708f;
	border-color: #31708f
}
.list-group-item-warning {
	color: #8a6d3b;
	background-color: #fcf8e3
}
a.list-group-item-warning, button.list-group-item-warning {
	color: #8a6d3b
}
a.list-group-item-warning .list-group-item-heading, button.list-group-item-warning .list-group-item-heading {
	color: inherit
}
a.list-group-item-warning:focus, a.list-group-item-warning:hover, button.list-group-item-warning:focus, button.list-group-item-warning:hover {
	color: #8a6d3b;
	background-color: #faf2cc
}
a.list-group-item-warning.active, a.list-group-item-warning.active:focus, a.list-group-item-warning.active:hover, button.list-group-item-warning.active, button.list-group-item-warning.active:focus, button.list-group-item-warning.active:hover {
	color: #fff;
	background-color: #8a6d3b;
	border-color: #8a6d3b
}
.list-group-item-danger {
	color: #a94442;
	background-color: #f2dede
}
a.list-group-item-danger, button.list-group-item-danger {
	color: #a94442
}
a.list-group-item-danger .list-group-item-heading, button.list-group-item-danger .list-group-item-heading {
	color: inherit
}
a.list-group-item-danger:focus, a.list-group-item-danger:hover, button.list-group-item-danger:focus, button.list-group-item-danger:hover {
	color: #a94442;
	background-color: #ebcccc
}
a.list-group-item-danger.active, a.list-group-item-danger.active:focus, a.list-group-item-danger.active:hover, button.list-group-item-danger.active, button.list-group-item-danger.active:focus, button.list-group-item-danger.active:hover {
	color: #fff;
	background-color: #a94442;
	border-color: #a94442
}
.list-group-item-heading {
	margin-top: 0;
	margin-bottom: 5px
}
.list-group-item-text {
	margin-bottom: 0;
	line-height: 1.3
}
.panel {
	margin-bottom: 20px;
	background-color: #fff;
	border: 1px solid transparent;
	border-radius: 4px;
	box-shadow: 0 1px 1px rgba(0,0,0,.05)
}
.panel-body {
	padding: 15px
}
.panel-heading {
	padding: 10px 15px;
	border-bottom: 1px solid transparent;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}
.panel-heading>.dropdown .dropdown-toggle, .panel-title {
	color: inherit
}
.panel-title {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 16px
}
.panel-title>.small, .panel-title>.small>a, .panel-title>a, .panel-title>small, .panel-title>small>a {
	color: inherit
}
.panel-footer {
	padding: 10px 15px;
	background-color: #f5f5f5;
	border-top: 1px solid #ddd;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}
.panel>.list-group, .panel>.panel-collapse>.list-group {
	margin-bottom: 0
}
.panel>.list-group .list-group-item, .panel>.panel-collapse>.list-group .list-group-item {
	border-width: 1px 0;
	border-radius: 0
}
.panel>.list-group:first-child .list-group-item:first-child, .panel>.panel-collapse>.list-group:first-child .list-group-item:first-child {
	border-top: 0;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}
.panel>.list-group:last-child .list-group-item:last-child, .panel>.panel-collapse>.list-group:last-child .list-group-item:last-child {
	border-bottom: 0;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}
.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child {
	border-top-left-radius: 0;
	border-top-right-radius: 0
}
.list-group+.panel-footer, .panel-heading+.list-group .list-group-item:first-child {
	border-top-width: 0
}
.panel>.panel-collapse>.table, .panel>.table, .panel>.table-responsive>.table {
	margin-bottom: 0
}
.panel>.panel-collapse>.table caption, .panel>.table caption, .panel>.table-responsive>.table caption {
	padding-right: 15px;
	padding-left: 15px
}
.panel>.table-responsive:first-child>.table:first-child, .panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child, .panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child, .panel>.table:first-child, .panel>.table:first-child>tbody:first-child>tr:first-child, .panel>.table:first-child>thead:first-child>tr:first-child {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child, .panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child, .panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child, .panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child, .panel>.table:first-child>tbody:first-child>tr:first-child td:first-child, .panel>.table:first-child>tbody:first-child>tr:first-child th:first-child, .panel>.table:first-child>thead:first-child>tr:first-child td:first-child, .panel>.table:first-child>thead:first-child>tr:first-child th:first-child {
	border-top-left-radius: 3px
}
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child, .panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child, .panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child, .panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child, .panel>.table:first-child>tbody:first-child>tr:first-child td:last-child, .panel>.table:first-child>tbody:first-child>tr:first-child th:last-child, .panel>.table:first-child>thead:first-child>tr:first-child td:last-child, .panel>.table:first-child>thead:first-child>tr:first-child th:last-child {
	border-top-right-radius: 3px
}
.panel>.table-responsive:last-child>.table:last-child, .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child, .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child, .panel>.table:last-child, .panel>.table:last-child>tbody:last-child>tr:last-child, .panel>.table:last-child>tfoot:last-child>tr:last-child {
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px
}
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child, .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child, .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child, .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child, .panel>.table:last-child>tbody:last-child>tr:last-child td:first-child, .panel>.table:last-child>tbody:last-child>tr:last-child th:first-child, .panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child, .panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
	border-bottom-left-radius: 3px
}
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child, .panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child, .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child, .panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child, .panel>.table:last-child>tbody:last-child>tr:last-child td:last-child, .panel>.table:last-child>tbody:last-child>tr:last-child th:last-child, .panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child, .panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
	border-bottom-right-radius: 3px
}
.panel>.panel-body+.table, .panel>.panel-body+.table-responsive, .panel>.table+.panel-body, .panel>.table-responsive+.panel-body {
	border-top: 1px solid #ddd
}
.panel>.table>tbody:first-child>tr:first-child td, .panel>.table>tbody:first-child>tr:first-child th {
	border-top: 0
}
.panel>.table-bordered, .panel>.table-responsive>.table-bordered {
	border: 0
}
.panel>.table-bordered>tbody>tr>td:first-child, .panel>.table-bordered>tbody>tr>th:first-child, .panel>.table-bordered>tfoot>tr>td:first-child, .panel>.table-bordered>tfoot>tr>th:first-child, .panel>.table-bordered>thead>tr>td:first-child, .panel>.table-bordered>thead>tr>th:first-child, .panel>.table-responsive>.table-bordered>tbody>tr>td:first-child, .panel>.table-responsive>.table-bordered>tbody>tr>th:first-child, .panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child, .panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child, .panel>.table-responsive>.table-bordered>thead>tr>td:first-child, .panel>.table-responsive>.table-bordered>thead>tr>th:first-child {
	border-left: 0
}
.panel>.table-bordered>tbody>tr>td:last-child, .panel>.table-bordered>tbody>tr>th:last-child, .panel>.table-bordered>tfoot>tr>td:last-child, .panel>.table-bordered>tfoot>tr>th:last-child, .panel>.table-bordered>thead>tr>td:last-child, .panel>.table-bordered>thead>tr>th:last-child, .panel>.table-responsive>.table-bordered>tbody>tr>td:last-child, .panel>.table-responsive>.table-bordered>tbody>tr>th:last-child, .panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child, .panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child, .panel>.table-responsive>.table-bordered>thead>tr>td:last-child, .panel>.table-responsive>.table-bordered>thead>tr>th:last-child {
	border-right: 0
}
.panel>.table-bordered>tbody>tr:first-child>td, .panel>.table-bordered>tbody>tr:first-child>th, .panel>.table-bordered>tbody>tr:last-child>td, .panel>.table-bordered>tbody>tr:last-child>th, .panel>.table-bordered>tfoot>tr:last-child>td, .panel>.table-bordered>tfoot>tr:last-child>th, .panel>.table-bordered>thead>tr:first-child>td, .panel>.table-bordered>thead>tr:first-child>th, .panel>.table-responsive>.table-bordered>tbody>tr:first-child>td, .panel>.table-responsive>.table-bordered>tbody>tr:first-child>th, .panel>.table-responsive>.table-bordered>tbody>tr:last-child>td, .panel>.table-responsive>.table-bordered>tbody>tr:last-child>th, .panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td, .panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th, .panel>.table-responsive>.table-bordered>thead>tr:first-child>td, .panel>.table-responsive>.table-bordered>thead>tr:first-child>th {
	border-bottom: 0
}
.panel>.table-responsive {
	margin-bottom: 0;
	border: 0
}
.panel-group {
	margin-bottom: 20px
}
.panel-group .panel {
	margin-bottom: 0;
	border-radius: 4px
}
.panel-group .panel+.panel {
	margin-top: 5px
}
.panel-group .panel-heading {
	border-bottom: 0
}
.panel-group .panel-heading+.panel-collapse>.list-group, .panel-group .panel-heading+.panel-collapse>.panel-body {
	border-top: 1px solid #ddd
}
.panel-group .panel-footer {
	border-top: 0
}
.panel-group .panel-footer+.panel-collapse .panel-body {
	border-bottom: 1px solid #ddd
}
.panel-default {
	border-color: #ddd
}
.panel-default>.panel-heading {
	color: #333;
	background-color: #f5f5f5;
	border-color: #ddd
}
.panel-default>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #ddd
}
.panel-default>.panel-heading .badge {
	color: #f5f5f5;
	background-color: #333
}
.panel-default>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #ddd
}
.panel-primary {
	border-color: #337ab7
}
.panel-primary>.panel-heading {
	color: #fff;
	background-color: #337ab7;
	border-color: #337ab7
}
.panel-primary>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #337ab7
}
.panel-primary>.panel-heading .badge {
	color: #337ab7;
	background-color: #fff
}
.panel-primary>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #337ab7
}
.panel-success {
	border-color: #d6e9c6
}
.panel-success>.panel-heading {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6
}
.panel-success>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #d6e9c6
}
.panel-success>.panel-heading .badge {
	color: #dff0d8;
	background-color: #3c763d
}
.panel-success>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #d6e9c6
}
.panel-info {
	border-color: #bce8f1
}
.panel-info>.panel-heading {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1
}
.panel-info>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #bce8f1
}
.panel-info>.panel-heading .badge {
	color: #d9edf7;
	background-color: #31708f
}
.panel-info>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #bce8f1
}
.panel-warning {
	border-color: #faebcc
}
.panel-warning>.panel-heading {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc
}
.panel-warning>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #faebcc
}
.panel-warning>.panel-heading .badge {
	color: #fcf8e3;
	background-color: #8a6d3b
}
.panel-warning>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #faebcc
}
.panel-danger {
	border-color: #ebccd1
}
.panel-danger>.panel-heading {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1
}
.panel-danger>.panel-heading+.panel-collapse>.panel-body {
	border-top-color: #ebccd1
}
.panel-danger>.panel-heading .badge {
	color: #f2dede;
	background-color: #a94442
}
.panel-danger>.panel-footer+.panel-collapse>.panel-body {
	border-bottom-color: #ebccd1
}
.embed-responsive {
	position: relative;
	display: block;
	height: 0;
	padding: 0;
	overflow: hidden
}
.embed-responsive .embed-responsive-item, .embed-responsive embed, .embed-responsive iframe, .embed-responsive object, .embed-responsive video {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 0
}
.embed-responsive-16by9 {
	padding-bottom: 56.25%
}
.embed-responsive-4by3 {
	padding-bottom: 75%
}
.well {
	min-height: 20px;
	padding: 19px;
	margin-bottom: 20px;
	background-color: #f5f5f5;
	border: 1px solid #e3e3e3;
	border-radius: 4px;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.05)
}
.well blockquote {
	border-color: #ddd;
	border-color: rgba(0,0,0,.15)
}
.well-lg {
	padding: 24px;
	border-radius: 6px
}
.well-sm {
	padding: 9px;
	border-radius: 3px
}
.close {
	float: right;
	font-size: 21px;
	font-weight: 700;
	line-height: 1;
	color: #000;
	text-shadow: 0 1px 0 #fff;
	filter: alpha(opacity=20);
	opacity: .2
}
.close:focus, .close:hover {
	color: #000;
	text-decoration: none;
	cursor: pointer;
	filter: alpha(opacity=50);
	opacity: .5
}
button.close {
	-webkit-appearance: none;
	padding: 0;
	cursor: pointer;
	background: 0 0;
	border: 0
}
.modal, .modal-open {
	overflow: hidden
}
.modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 13;
	display: none;
	-webkit-overflow-scrolling: touch;
	outline: 0
}
.modal.fade .modal-dialog {
	transition: -webkit-transform .3s ease-out;
	transition: transform .3s ease-out;
	-webkit-transform: translate(0, -25%);
	transform: translate(0, -25%)
}
.modal.in .modal-dialog {
	-webkit-transform: translate(0, 0);
	transform: translate(0, 0)
}
.modal-open .modal {
	overflow-x: hidden;
	overflow-y: auto
}
.modal-dialog {
	position: relative;
	width: auto;
	margin: 10px
}
.modal-content {
	position: relative;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #999;
	border: 1px solid rgba(0,0,0,.2);
	border-radius: 6px;
	outline: 0;
	box-shadow: 0 3px 9px rgba(0,0,0,.5)
}
.modal-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 12;
	background-color: #000
}
.modal-backdrop.fade {
	filter: alpha(opacity=0);
	opacity: 0
}
.modal-backdrop.in {
	filter: alpha(opacity=50);
	opacity: .5
}
.modal-header {
	padding: 15px;
	border-bottom: 1px solid #e5e5e5
}
.modal-header .close {
	margin-top: -2px
}
.modal-title {
	margin: 0;
	line-height: 1.42857143
}
.modal-body {
	position: relative;
	padding: 15px
}
.modal-footer {
	padding: 15px;
	text-align: right;
	border-top: 1px solid #e5e5e5
}
.modal-footer .btn+.btn {
	margin-bottom: 0;
	margin-left: 5px
}
.modal-footer .btn-group .btn+.btn {
	margin-left: -1px
}
.modal-footer .btn-block+.btn-block {
	margin-left: 0
}
.modal-scrollbar-measure {
	position: absolute;
	top: -9999px;
	width: 50px;
	height: 50px;
	overflow: scroll
}
@media (min-width:768px) {
.modal-dialog {
	width: 600px;
	margin: 30px auto
}
.modal-content {
	box-shadow: 0 5px 15px rgba(0,0,0,.5)
}
.modal-sm {
	width: 300px
}
}
@media (min-width:992px) {
.modal-lg {
	width: 900px
}
}
.tooltip {
	position: absolute;
	z-index: 15;
	display: block;
	font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: left;
	text-align: start;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	word-wrap: normal;
	white-space: normal;
	filter: alpha(opacity=0);
	opacity: 0;
	line-break: auto
}
.tooltip.in {
	filter: alpha(opacity=90);
	opacity: .9
}
.tooltip.top {
	padding: 5px 0;
	margin-top: -3px
}
.tooltip.right {
	padding: 0 5px;
	margin-left: 3px
}
.tooltip.bottom {
	padding: 5px 0;
	margin-top: 3px
}
.tooltip.left {
	padding: 0 5px;
	margin-left: -3px
}
.tooltip-inner {
	max-width: 200px;
	padding: 3px 8px;
	color: #fff;
	text-align: center;
	background-color: #000;
	border-radius: 4px
}
.tooltip-arrow {
	position: absolute;
	width: 0;
	height: 0;
	border-color: transparent;
	border-style: solid
}
.tooltip.top .tooltip-arrow {
	bottom: 0;
	left: 50%;
	margin-left: -5px;
	border-width: 5px 5px 0;
	border-top-color: #000
}
.tooltip.top-left .tooltip-arrow {
	right: 5px
}
.tooltip.top-left .tooltip-arrow, .tooltip.top-right .tooltip-arrow {
	bottom: 0;
	margin-bottom: -5px;
	border-width: 5px 5px 0;
	border-top-color: #000
}
.tooltip.top-right .tooltip-arrow {
	left: 5px
}
.tooltip.right .tooltip-arrow {
	top: 50%;
	left: 0;
	margin-top: -5px;
	border-width: 5px 5px 5px 0;
	border-right-color: #000
}
.tooltip.left .tooltip-arrow {
	top: 50%;
	right: 0;
	margin-top: -5px;
	border-width: 5px 0 5px 5px;
	border-left-color: #000
}
.tooltip.bottom .tooltip-arrow {
	top: 0;
	left: 50%;
	margin-left: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}
.tooltip.bottom-left .tooltip-arrow {
	top: 0;
	right: 5px;
	margin-top: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}
.tooltip.bottom-right .tooltip-arrow {
	top: 0;
	left: 5px;
	margin-top: -5px;
	border-width: 0 5px 5px;
	border-bottom-color: #000
}
.popover {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 14;
	display: none;
	max-width: 276px;
	padding: 1px;
	font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: left;
	text-align: start;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	word-wrap: normal;
	white-space: normal;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #ccc;
	border: 1px solid rgba(0,0,0,.2);
	border-radius: 6px;
	box-shadow: 0 5px 10px rgba(0,0,0,.2);
	line-break: auto
}
.popover.top {
	margin-top: -10px
}
.popover.right {
	margin-left: 10px
}
.popover.bottom {
	margin-top: 10px
}
.popover.left {
	margin-left: -10px
}
.popover-title {
	padding: 8px 14px;
	margin: 0;
	font-size: 14px;
	background-color: #f7f7f7;
	border-bottom: 1px solid #ebebeb;
	border-radius: 5px 5px 0 0
}
.popover-content {
	padding: 9px 14px
}
.popover>.arrow, .popover>.arrow:after {
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	border-color: transparent;
	border-style: solid
}
.popover>.arrow {
	border-width: 11px
}
.popover>.arrow:after {
	content: "";
	border-width: 10px
}
.popover.top>.arrow {
	bottom: -11px;
	left: 50%;
	margin-left: -11px;
	border-top-color: #999;
	border-top-color: rgba(0,0,0,.25);
	border-bottom-width: 0
}
.popover.top>.arrow:after {
	bottom: 1px;
	margin-left: -10px;
	content: " ";
	border-top-color: #fff;
	border-bottom-width: 0
}
.popover.right>.arrow {
	top: 50%;
	left: -11px;
	margin-top: -11px;
	border-right-color: #999;
	border-right-color: rgba(0,0,0,.25);
	border-left-width: 0
}
.popover.right>.arrow:after {
	bottom: -10px;
	left: 1px;
	content: " ";
	border-right-color: #fff;
	border-left-width: 0
}
.popover.bottom>.arrow {
	top: -11px;
	left: 50%;
	margin-left: -11px;
	border-top-width: 0;
	border-bottom-color: #999;
	border-bottom-color: rgba(0,0,0,.25)
}
.popover.bottom>.arrow:after {
	top: 1px;
	margin-left: -10px;
	content: " ";
	border-top-width: 0;
	border-bottom-color: #fff
}
.popover.left>.arrow {
	top: 50%;
	right: -11px;
	margin-top: -11px;
	border-right-width: 0;
	border-left-color: #999;
	border-left-color: rgba(0,0,0,.25)
}
.popover.left>.arrow:after {
	right: 1px;
	bottom: -10px;
	content: " ";
	border-right-width: 0;
	border-left-color: #fff
}
.carousel, .carousel-inner {
	position: relative
}
.carousel-inner {
	width: 100%;
	overflow: hidden
}
.carousel-inner>.item {
	position: relative;
	display: none;
	transition: .6s ease-in-out left
}
.carousel-inner>.item>a>img, .carousel-inner>.item>img {
	line-height: 1
}
@media (-webkit-transform-3d), all and (transform-3d) {
.carousel-inner>.item {
	transition: -webkit-transform .6s ease-in-out;
	transition: transform .6s ease-in-out;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-perspective: 1000px;
	perspective: 1000px
}
.carousel-inner>.item.active.right, .carousel-inner>.item.next {
	left: 0;
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0)
}
.carousel-inner>.item.active.left, .carousel-inner>.item.prev {
	left: 0;
	-webkit-transform: translate3d(-100%, 0, 0);
	transform: translate3d(-100%, 0, 0)
}
.carousel-inner>.item.active, .carousel-inner>.item.next.left, .carousel-inner>.item.prev.right {
	left: 0;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}
}
.carousel-inner>.active, .carousel-inner>.next, .carousel-inner>.prev {
	display: block
}
.carousel-inner>.active {
	left: 0
}
.carousel-inner>.next, .carousel-inner>.prev {
	position: absolute;
	top: 0;
	width: 100%;
}
.carousel-inner>.next {
	left: 100%;
}
.carousel-inner>.prev {
	left: -100%;
}
.carousel-inner>.next.left, .carousel-inner>.prev.right {
	left: 0;
}
.carousel-inner>.active.left {
	left: -100%;
}
.carousel-inner>.active.right {
	left: 100%;
}
.carousel-control {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 15%;
	font-size: 20px;
	color: #fff;
	text-align: center;
	text-shadow: 0 1px 2px rgba(0,0,0,.6);
	background-color: transparent;
	filter: alpha(opacity=50);
	opacity: .5;
}
.carousel-control.left {
	background-image: linear-gradient(to right, rgba(0,0,0,.5) 0, rgba(0,0,0,.0001) 100%);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
	background-repeat: repeat-x;
}
.carousel-control.right {
	right: 0;
	left: auto;
	background-image: linear-gradient(to right, rgba(0,0,0,.0001) 0, rgba(0,0,0,.5) 100%);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
	background-repeat: repeat-x;
}
.carousel-control:focus, .carousel-control:hover {
	color: #fff;
	text-decoration: none;
	filter: alpha(opacity=90);
	outline: 0;
	opacity: .9;
}
.carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next, .carousel-control .icon-prev {
	position: absolute;
	top: 50%;
	z-index: 3;
	display: inline-block;
	margin-top: -10px
}
.carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {
	left: 50%;
	margin-left: -10px;
}
.carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {
	right: 50%;
	margin-right: -10px;
}
.carousel-control .icon-next, .carousel-control .icon-prev {
	width: 20px;
	height: 20px;
	font-family: serif;
	line-height: 1;
}
.carousel-control .icon-prev:before {
	content: '\2039';
}
.carousel-control .icon-next:before {
	content: '\203a';
}
.carousel-indicators {
	position: absolute;
	bottom: 10px;
	left: 50%;
	z-index: 6;
	width: 60%;
	padding-left: 0;
	margin-left: -30%;
	text-align: center;
	list-style: none
}
.carousel-indicators li {
	display: inline-block;
	width: 10px;
	height: 10px;
	margin: 1px;
	text-indent: -999px;
	cursor: pointer;
	background-color: #000\9;
	background-color: transparent;
	border: 1px solid #fff;
	border-radius: 10px;
}
.carousel-indicators .active {
	width: 12px;
	height: 12px;
	margin: 0;
	background-color: #fff;
}
.carousel-caption {
	position: absolute;
	right: 15%;
	bottom: 20px;
	left: 15%;
	z-index: 5;
	padding-top: 20px;
	padding-bottom: 20px;
	color: #fff;
	text-align: center;
	text-shadow: 0 1px 2px rgba(0,0,0,.6);
}
.carousel-caption .btn {
	text-shadow: none
}
@media screen and (min-width:768px) {
.carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next, .carousel-control .icon-prev {
	width: 30px;
	height: 30px;
	margin-top: -10px;
	font-size: 30px
}
.carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {
	margin-left: -10px
}
.carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {
	margin-right: -10px
}
.carousel-caption {
	right: 20%;
	left: 20%;
	padding-bottom: 30px
}
.carousel-indicators {
	bottom: 20px
}
}
.btn-group-vertical>.btn-group:after, .btn-group-vertical>.btn-group:before, .btn-toolbar:after, .btn-toolbar:before, .clearfix:after, .clearfix:before, .container-fluid:after, .container-fluid:before, .container:after, .container:before, .dl-horizontal dd:after, .dl-horizontal dd:before, .form-horizontal .form-group:after, .form-horizontal .form-group:before, .modal-footer:after, .modal-footer:before, .modal-header:after, .modal-header:before, .nav:after, .nav:before, .navbar-collapse:after, .navbar-collapse:before, .navbar-header:after, .navbar-header:before, .navbar:after, .navbar:before, .pager:after, .pager:before, .panel-body:after, .panel-body:before, .row:after, .row:before {
	display: table;
	content: " "
}
.btn-group-vertical>.btn-group:after, .btn-toolbar:after, .clearfix:after, .container-fluid:after, .container:after, .dl-horizontal dd:after, .form-horizontal .form-group:after, .modal-footer:after, .modal-header:after, .nav:after, .navbar-collapse:after, .navbar-header:after, .navbar:after, .pager:after, .panel-body:after, .row:after {
	clear: both
}
.center-block {
	display: block;
	margin-right: auto;
	margin-left: auto
}
.pull-right {
	float: right!important
}
.pull-left {
	float: left!important
}
.hide {
	display: none!important
}
.show {
	display: block!important
}
.invisible {
	visibility: hidden
}
.text-hide {
	font: 0/0 a;
	color: transparent;
	text-shadow: none;
	background-color: transparent;
	border: 0
}
.hidden {
	display: none!important
}
.affix {
	position: fixed
}
@-ms-viewport {
width:device-width
}
.visible-lg, .visible-lg-block, .visible-lg-inline, .visible-lg-inline-block, .visible-md, .visible-md-block, .visible-md-inline, .visible-md-inline-block, .visible-sm, .visible-sm-block, .visible-sm-inline, .visible-sm-inline-block, .visible-xs, .visible-xs-block, .visible-xs-inline, .visible-xs-inline-block {
	display: none!important
}
@media (max-width:767px) {
.visible-xs {
	display: block!important
}
table.visible-xs {
	display: table!important
}
tr.visible-xs {
	display: table-row!important
}
td.visible-xs, th.visible-xs {
	display: table-cell!important
}
}
@media (max-width:767px) {
.visible-xs-block {
	display: block!important
}
}
@media (max-width:767px) {
.visible-xs-inline {
	display: inline!important
}
}
@media (max-width:767px) {
.visible-xs-inline-block {
	display: inline-block!important
}
}
@media (min-width:768px) and (max-width:991px) {
.visible-sm {
	display: block!important
}
table.visible-sm {
	display: table!important
}
tr.visible-sm {
	display: table-row!important
}
td.visible-sm, th.visible-sm {
	display: table-cell!important
}
}
@media (min-width:768px) and (max-width:991px) {
.visible-sm-block {
	display: block!important
}
}
@media (min-width:768px) and (max-width:991px) {
.visible-sm-inline {
	display: inline!important
}
}
@media (min-width:768px) and (max-width:991px) {
.visible-sm-inline-block {
	display: inline-block!important
}
}
@media (min-width:992px) and (max-width:1199px) {
.visible-md {
	display: block!important
}
table.visible-md {
	display: table!important
}
tr.visible-md {
	display: table-row!important
}
td.visible-md, th.visible-md {
	display: table-cell!important
}
}
@media (min-width:992px) and (max-width:1199px) {
.visible-md-block {
	display: block!important
}
}
@media (min-width:992px) and (max-width:1199px) {
.visible-md-inline {
	display: inline!important
}
}
@media (min-width:992px) and (max-width:1199px) {
.visible-md-inline-block {
	display: inline-block!important
}
}
@media (min-width:1200px) {
.visible-lg {
	display: block!important
}
table.visible-lg {
	display: table!important
}
tr.visible-lg {
	display: table-row!important
}
td.visible-lg, th.visible-lg {
	display: table-cell!important
}
}
@media (min-width:1200px) {
.visible-lg-block {
	display: block!important
}
}
@media (min-width:1200px) {
.visible-lg-inline {
	display: inline!important
}
}
@media (min-width:1200px) {
.visible-lg-inline-block {
	display: inline-block!important
}
}
@media (max-width:767px) {
.hidden-xs {
	display: none!important
}
}
@media (min-width:768px) and (max-width:991px) {
.hidden-sm {
	display: none!important
}
}
@media (min-width:992px) and (max-width:1199px) {
.hidden-md {
	display: none!important
}
}
@media (min-width:1200px) {
.hidden-lg {
	display: none!important
}
}
.visible-print {
	display: none!important
}
@media print {
.visible-print {
	display: block!important
}
table.visible-print {
	display: table!important
}
tr.visible-print {
	display: table-row!important
}
td.visible-print, th.visible-print {
	display: table-cell!important
}
}
.visible-print-block {
	display: none!important
}
@media print {
.visible-print-block {
	display: block!important
}
}
.visible-print-inline {
	display: none!important
}
@media print {
.visible-print-inline {
	display: inline!important
}
}
.visible-print-inline-block {
	display: none!important
}
@media print {
.visible-print-inline-block {
	display: inline-block!important
}
}
@media print {
.hidden-print {
	display: none!important
}
}
/*!
 *  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */@font-face {
	font-family: FontAwesome;
	src: url(../fonts/fontawesome-webfont.eot?v=4.5.0);
	src: url(../fonts/fontawesome-webfont.eot?#iefix&v=4.5.0) format('embedded-opentype'), url(../fonts/fontawesome-webfont.woff2?v=4.5.0) format('woff2'), url(../fonts/fontawesome-webfont.woff?v=4.5.0) format('woff'), url(../fonts/fontawesome-webfont.ttf?v=4.5.0) format('truetype'), url(../fonts/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular) format('svg');
	font-weight: 400;
	font-style: normal
}
.fa {
	display: inline-block;
	font: normal normal normal 14px/1 FontAwesome;
	font-size: inherit;
	text-rendering: auto;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale
}
.fa-lg {
	font-size: 1.33333333em;
	line-height: .75em;
	vertical-align: -15%
}
.fa-2x {
	font-size: 2em
}
.fa-3x {
	font-size: 3em
}
.fa-4x {
	font-size: 4em
}
.fa-5x {
	font-size: 5em
}
.fa-fw {
	width: 1.28571429em;
	text-align: center
}
.fa-ul {
	padding-left: 0;
	margin-left: 2.14285714em;
	list-style-type: none
}
.fa-ul>li {
	position: relative
}
.fa-li {
	position: absolute;
	left: -2.14285714em;
	width: 2.14285714em;
	top: .14285714em;
	text-align: center
}
.fa-li.fa-lg {
	left: -1.85714286em
}
.fa-border {
	padding: .2em .25em .15em;
	border: .08em solid #eee;
	border-radius: .1em
}
.fa-pull-left {
	float: left
}
.fa-pull-right {
	float: right
}
.fa.fa-pull-left {
	margin-right: .3em
}
.fa.fa-pull-right {
	margin-left: .3em
}
.pull-right {
	float: right
}
.pull-left {
	float: left
}
.fa.pull-left {
	margin-right: .3em
}
.fa.pull-right {
	margin-left: .3em
}
.fa-spin {
	-webkit-animation: b 2s infinite linear;
	animation: b 2s infinite linear
}
.fa-pulse {
	-webkit-animation: b 1s infinite steps(8);
	animation: b 1s infinite steps(8)
}
@-webkit-keyframes b {
0% {
-webkit-transform:rotate(0deg);
transform:rotate(0deg)
}
to {
	-webkit-transform: rotate(359deg);
	transform: rotate(359deg)
}
}
@keyframes b {
0% {
-webkit-transform:rotate(0deg);
transform:rotate(0deg)
}
to {
	-webkit-transform: rotate(359deg);
	transform: rotate(359deg)
}
}
.fa-rotate-90 {
filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg)
}
.fa-rotate-180 {
filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg)
}
.fa-rotate-270 {
filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
	-webkit-transform: rotate(270deg);
	transform: rotate(270deg)
}
.fa-flip-horizontal {
filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
	-webkit-transform: scale(-1, 1);
	transform: scale(-1, 1)
}
.fa-flip-vertical {
filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
	-webkit-transform: scale(1, -1);
	transform: scale(1, -1)
}
:root .fa-flip-horizontal, :root .fa-flip-vertical, :root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270 {
	-webkit-filter: none;
	filter: none
}
.fa-stack {
	position: relative;
	display: inline-block;
	width: 2em;
	height: 2em;
	line-height: 2em;
	vertical-align: middle
}
.fa-stack-1x, .fa-stack-2x {
	position: absolute;
	left: 0;
	width: 100%;
	text-align: center
}
.fa-stack-1x {
	line-height: inherit
}
.fa-stack-2x {
	font-size: 2em
}
.fa-inverse {
	color: #fff
}
.fa-glass:before {
	content: "\f000"
}
.fa-music:before {
	content: "\f001"
}
.fa-search:before {
	content: "\f002"
}
.fa-envelope-o:before {
	content: "\f003"
}
.fa-heart:before {
	content: "\f004"
}
.fa-star:before {
	content: "\f005"
}
.fa-star-o:before {
	content: "\f006"
}
.fa-user:before {
	content: "\f007"
}
.fa-film:before {
	content: "\f008"
}
.fa-th-large:before {
	content: "\f009"
}
.fa-th:before {
	content: "\f00a"
}
.fa-th-list:before {
	content: "\f00b"
}
.fa-check:before {
	content: "\f00c"
}
.fa-close:before, .fa-remove:before, .fa-times:before {
	content: "\f00d"
}
.fa-search-plus:before {
	content: "\f00e"
}
.fa-search-minus:before {
	content: "\f010"
}
.fa-power-off:before {
	content: "\f011"
}
.fa-signal:before {
	content: "\f012"
}
.fa-cog:before, .fa-gear:before {
	content: "\f013"
}
.fa-trash-o:before {
	content: "\f014"
}
.fa-home:before {
	content: "\f015"
}
.fa-file-o:before {
	content: "\f016"
}
.fa-clock-o:before {
	content: "\f017"
}
.fa-road:before {
	content: "\f018"
}
.fa-download:before {
	content: "\f019"
}
.fa-arrow-circle-o-down:before {
	content: "\f01a"
}
.fa-arrow-circle-o-up:before {
	content: "\f01b"
}
.fa-inbox:before {
	content: "\f01c"
}
.fa-play-circle-o:before {
	content: "\f01d"
}
.fa-repeat:before, .fa-rotate-right:before {
	content: "\f01e"
}
.fa-refresh:before {
	content: "\f021"
}
.fa-list-alt:before {
	content: "\f022"
}
.fa-lock:before {
	content: "\f023"
}
.fa-flag:before {
	content: "\f024"
}
.fa-headphones:before {
	content: "\f025"
}
.fa-volume-off:before {
	content: "\f026"
}
.fa-volume-down:before {
	content: "\f027"
}
.fa-volume-up:before {
	content: "\f028"
}
.fa-qrcode:before {
	content: "\f029"
}
.fa-barcode:before {
	content: "\f02a"
}
.fa-tag:before {
	content: "\f02b"
}
.fa-tags:before {
	content: "\f02c"
}
.fa-book:before {
	content: "\f02d"
}
.fa-bookmark:before {
	content: "\f02e"
}
.fa-print:before {
	content: "\f02f"
}
.fa-camera:before {
	content: "\f030"
}
.fa-font:before {
	content: "\f031"
}
.fa-bold:before {
	content: "\f032"
}
.fa-italic:before {
	content: "\f033"
}
.fa-text-height:before {
	content: "\f034"
}
.fa-text-width:before {
	content: "\f035"
}
.fa-align-left:before {
	content: "\f036"
}
.fa-align-center:before {
	content: "\f037"
}
.fa-align-right:before {
	content: "\f038"
}
.fa-align-justify:before {
	content: "\f039"
}
.fa-list:before {
	content: "\f03a"
}
.fa-dedent:before, .fa-outdent:before {
	content: "\f03b"
}
.fa-indent:before {
	content: "\f03c"
}
.fa-video-camera:before {
	content: "\f03d"
}
.fa-image:before, .fa-photo:before, .fa-picture-o:before {
	content: "\f03e"
}
.fa-pencil:before {
	content: "\f040"
}
.fa-map-marker:before {
	content: "\f041"
}
.fa-adjust:before {
	content: "\f042"
}
.fa-tint:before {
	content: "\f043"
}
.fa-edit:before, .fa-pencil-square-o:before {
	content: "\f044"
}
.fa-share-square-o:before {
	content: "\f045"
}
.fa-check-square-o:before {
	content: "\f046"
}
.fa-arrows:before {
	content: "\f047"
}
.fa-step-backward:before {
	content: "\f048"
}
.fa-fast-backward:before {
	content: "\f049"
}
.fa-backward:before {
	content: "\f04a"
}
.fa-play:before {
	content: "\f04b"
}
.fa-pause:before {
	content: "\f04c"
}
.fa-stop:before {
	content: "\f04d"
}
.fa-forward:before {
	content: "\f04e"
}
.fa-fast-forward:before {
	content: "\f050"
}
.fa-step-forward:before {
	content: "\f051"
}
.fa-eject:before {
	content: "\f052"
}
.fa-chevron-left:before {
	content: "\f053"
}
.fa-chevron-right:before {
	content: "\f054"
}
.fa-plus-circle:before {
	content: "\f055"
}
.fa-minus-circle:before {
	content: "\f056"
}
.fa-times-circle:before {
	content: "\f057"
}
.fa-check-circle:before {
	content: "\f058"
}
.fa-question-circle:before {
	content: "\f059"
}
.fa-info-circle:before {
	content: "\f05a"
}
.fa-crosshairs:before {
	content: "\f05b"
}
.fa-times-circle-o:before {
	content: "\f05c"
}
.fa-check-circle-o:before {
	content: "\f05d"
}
.fa-ban:before {
	content: "\f05e"
}
.fa-arrow-left:before {
	content: "\f060"
}
.fa-arrow-right:before {
	content: "\f061"
}
.fa-arrow-up:before {
	content: "\f062"
}
.fa-arrow-down:before {
	content: "\f063"
}
.fa-mail-forward:before, .fa-share:before {
	content: "\f064"
}
.fa-expand:before {
	content: "\f065"
}
.fa-compress:before {
	content: "\f066"
}
.fa-plus:before {
	content: "\f067"
}
.fa-minus:before {
	content: "\f068"
}
.fa-asterisk:before {
	content: "\f069"
}
.fa-exclamation-circle:before {
	content: "\f06a"
}
.fa-gift:before {
	content: "\f06b"
}
.fa-leaf:before {
	content: "\f06c"
}
.fa-fire:before {
	content: "\f06d"
}
.fa-eye:before {
	content: "\f06e"
}
.fa-eye-slash:before {
	content: "\f070"
}
.fa-exclamation-triangle:before, .fa-warning:before {
	content: "\f071"
}
.fa-plane:before {
	content: "\f072"
}
.fa-calendar:before {
	content: "\f073"
}
.fa-random:before {
	content: "\f074"
}
.fa-comment:before {
	content: "\f075"
}
.fa-magnet:before {
	content: "\f076"
}
.fa-chevron-up:before {
	content: "\f077"
}
.fa-chevron-down:before {
	content: "\f078"
}
.fa-retweet:before {
	content: "\f079"
}
.fa-shopping-cart:before {
	content: "\f07a"
}
.fa-folder:before {
	content: "\f07b"
}
.fa-folder-open:before {
	content: "\f07c"
}
.fa-arrows-v:before {
	content: "\f07d"
}
.fa-arrows-h:before {
	content: "\f07e"
}
.fa-bar-chart-o:before, .fa-bar-chart:before {
	content: "\f080"
}
.fa-twitter-square:before {
	content: "\f081"
}
.fa-facebook-square:before {
	content: "\f082"
}
.fa-camera-retro:before {
	content: "\f083"
}
.fa-key:before {
	content: "\f084"
}
.fa-cogs:before, .fa-gears:before {
	content: "\f085"
}
.fa-comments:before {
	content: "\f086"
}
.fa-thumbs-o-up:before {
	content: "\f087"
}
.fa-thumbs-o-down:before {
	content: "\f088"
}
.fa-star-half:before {
	content: "\f089"
}
.fa-heart-o:before {
	content: "\f08a"
}
.fa-sign-out:before {
	content: "\f08b"
}
.fa-linkedin-square:before {
	content: "\f08c"
}
.fa-thumb-tack:before {
	content: "\f08d"
}
.fa-external-link:before {
	content: "\f08e"
}
.fa-sign-in:before {
	content: "\f090"
}
.fa-trophy:before {
	content: "\f091"
}
.fa-github-square:before {
	content: "\f092"
}
.fa-upload:before {
	content: "\f093"
}
.fa-lemon-o:before {
	content: "\f094"
}
.fa-phone:before {
	content: "\f095"
}
.fa-square-o:before {
	content: "\f096"
}
.fa-bookmark-o:before {
	content: "\f097"
}
.fa-phone-square:before {
	content: "\f098"
}
.fa-twitter:before {
	content: "\f099"
}
.fa-facebook-f:before, .fa-facebook:before {
	content: "\f09a"
}
.fa-github:before {
	content: "\f09b"
}
.fa-unlock:before {
	content: "\f09c"
}
.fa-credit-card:before {
	content: "\f09d"
}
.fa-feed:before, .fa-rss:before {
	content: "\f09e"
}
.fa-hdd-o:before {
	content: "\f0a0"
}
.fa-bullhorn:before {
	content: "\f0a1"
}
.fa-bell:before {
	content: "\f0f3"
}
.fa-certificate:before {
	content: "\f0a3"
}
.fa-hand-o-right:before {
	content: "\f0a4"
}
.fa-hand-o-left:before {
	content: "\f0a5"
}
.fa-hand-o-up:before {
	content: "\f0a6"
}
.fa-hand-o-down:before {
	content: "\f0a7"
}
.fa-arrow-circle-left:before {
	content: "\f0a8"
}
.fa-arrow-circle-right:before {
	content: "\f0a9"
}
.fa-arrow-circle-up:before {
	content: "\f0aa"
}
.fa-arrow-circle-down:before {
	content: "\f0ab"
}
.fa-globe:before {
	content: "\f0ac"
}
.fa-wrench:before {
	content: "\f0ad"
}
.fa-tasks:before {
	content: "\f0ae"
}
.fa-filter:before {
	content: "\f0b0"
}
.fa-briefcase:before {
	content: "\f0b1"
}
.fa-arrows-alt:before {
	content: "\f0b2"
}
.fa-group:before, .fa-users:before {
	content: "\f0c0"
}
.fa-chain:before, .fa-link:before {
	content: "\f0c1"
}
.fa-cloud:before {
	content: "\f0c2"
}
.fa-flask:before {
	content: "\f0c3"
}
.fa-cut:before, .fa-scissors:before {
	content: "\f0c4"
}
.fa-copy:before, .fa-files-o:before {
	content: "\f0c5"
}
.fa-paperclip:before {
	content: "\f0c6"
}
.fa-floppy-o:before, .fa-save:before {
	content: "\f0c7"
}
.fa-square:before {
	content: "\f0c8"
}
.fa-bars:before, .fa-navicon:before, .fa-reorder:before {
	content: "\f0c9"
}
.fa-list-ul:before {
	content: "\f0ca"
}
.fa-list-ol:before {
	content: "\f0cb"
}
.fa-strikethrough:before {
	content: "\f0cc"
}
.fa-underline:before {
	content: "\f0cd"
}
.fa-table:before {
	content: "\f0ce"
}
.fa-magic:before {
	content: "\f0d0"
}
.fa-truck:before {
	content: "\f0d1"
}
.fa-pinterest:before {
	content: "\f0d2"
}
.fa-pinterest-square:before {
	content: "\f0d3"
}
.fa-google-plus-square:before {
	content: "\f0d4"
}
.fa-google-plus:before {
	content: "\f0d5"
}
.fa-money:before {
	content: "\f0d6"
}
.fa-caret-down:before {
	content: "\f0d7"
}
.fa-caret-up:before {
	content: "\f0d8"
}
.fa-caret-left:before {
	content: "\f0d9"
}
.fa-caret-right:before {
	content: "\f0da"
}
.fa-columns:before {
	content: "\f0db"
}
.fa-sort:before, .fa-unsorted:before {
	content: "\f0dc"
}
.fa-sort-desc:before, .fa-sort-down:before {
	content: "\f0dd"
}
.fa-sort-asc:before, .fa-sort-up:before {
	content: "\f0de"
}
.fa-envelope:before {
	content: "\f0e0"
}
.fa-linkedin:before {
	content: "\f0e1"
}
.fa-rotate-left:before, .fa-undo:before {
	content: "\f0e2"
}
.fa-gavel:before, .fa-legal:before {
	content: "\f0e3"
}
.fa-dashboard:before, .fa-tachometer:before {
	content: "\f0e4"
}
.fa-comment-o:before {
	content: "\f0e5"
}
.fa-comments-o:before {
	content: "\f0e6"
}
.fa-bolt:before, .fa-flash:before {
	content: "\f0e7"
}
.fa-sitemap:before {
	content: "\f0e8"
}
.fa-umbrella:before {
	content: "\f0e9"
}
.fa-clipboard:before, .fa-paste:before {
	content: "\f0ea"
}
.fa-lightbulb-o:before {
	content: "\f0eb"
}
.fa-exchange:before {
	content: "\f0ec"
}
.fa-cloud-download:before {
	content: "\f0ed"
}
.fa-cloud-upload:before {
	content: "\f0ee"
}
.fa-user-md:before {
	content: "\f0f0"
}
.fa-stethoscope:before {
	content: "\f0f1"
}
.fa-suitcase:before {
	content: "\f0f2"
}
.fa-bell-o:before {
	content: "\f0a2"
}
.fa-coffee:before {
	content: "\f0f4"
}
.fa-cutlery:before {
	content: "\f0f5"
}
.fa-file-text-o:before {
	content: "\f0f6"
}
.fa-building-o:before {
	content: "\f0f7"
}
.fa-hospital-o:before {
	content: "\f0f8"
}
.fa-ambulance:before {
	content: "\f0f9"
}
.fa-medkit:before {
	content: "\f0fa"
}
.fa-fighter-jet:before {
	content: "\f0fb"
}
.fa-beer:before {
	content: "\f0fc"
}
.fa-h-square:before {
	content: "\f0fd"
}
.fa-plus-square:before {
	content: "\f0fe"
}
.fa-angle-double-left:before {
	content: "\f100"
}
.fa-angle-double-right:before {
	content: "\f101"
}
.fa-angle-double-up:before {
	content: "\f102"
}
.fa-angle-double-down:before {
	content: "\f103"
}
.fa-angle-left:before {
	content: "\f104"
}
.fa-angle-right:before {
	content: "\f105"
}
.fa-angle-up:before {
	content: "\f106"
}
.fa-angle-down:before {
	content: "\f107"
}
.fa-desktop:before {
	content: "\f108"
}
.fa-laptop:before {
	content: "\f109"
}
.fa-tablet:before {
	content: "\f10a"
}
.fa-mobile-phone:before, .fa-mobile:before {
	content: "\f10b"
}
.fa-circle-o:before {
	content: "\f10c"
}
.fa-quote-left:before {
	content: "\f10d"
}
.fa-quote-right:before {
	content: "\f10e"
}
.fa-spinner:before {
	content: "\f110"
}
.fa-circle:before {
	content: "\f111"
}
.fa-mail-reply:before, .fa-reply:before {
	content: "\f112"
}
.fa-github-alt:before {
	content: "\f113"
}
.fa-folder-o:before {
	content: "\f114"
}
.fa-folder-open-o:before {
	content: "\f115"
}
.fa-smile-o:before {
	content: "\f118"
}
.fa-frown-o:before {
	content: "\f119"
}
.fa-meh-o:before {
	content: "\f11a"
}
.fa-gamepad:before {
	content: "\f11b"
}
.fa-keyboard-o:before {
	content: "\f11c"
}
.fa-flag-o:before {
	content: "\f11d"
}
.fa-flag-checkered:before {
	content: "\f11e"
}
.fa-terminal:before {
	content: "\f120"
}
.fa-code:before {
	content: "\f121"
}
.fa-mail-reply-all:before, .fa-reply-all:before {
	content: "\f122"
}
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
	content: "\f123"
}
.fa-location-arrow:before {
	content: "\f124"
}
.fa-crop:before {
	content: "\f125"
}
.fa-code-fork:before {
	content: "\f126"
}
.fa-chain-broken:before, .fa-unlink:before {
	content: "\f127"
}
.fa-question:before {
	content: "\f128"
}
.fa-info:before {
	content: "\f129"
}
.fa-exclamation:before {
	content: "\f12a"
}
.fa-superscript:before {
	content: "\f12b"
}
.fa-subscript:before {
	content: "\f12c"
}
.fa-eraser:before {
	content: "\f12d"
}
.fa-puzzle-piece:before {
	content: "\f12e"
}
.fa-microphone:before {
	content: "\f130"
}
.fa-microphone-slash:before {
	content: "\f131"
}
.fa-shield:before {
	content: "\f132"
}
.fa-calendar-o:before {
	content: "\f133"
}
.fa-fire-extinguisher:before {
	content: "\f134"
}
.fa-rocket:before {
	content: "\f135"
}
.fa-maxcdn:before {
	content: "\f136"
}
.fa-chevron-circle-left:before {
	content: "\f137"
}
.fa-chevron-circle-right:before {
	content: "\f138"
}
.fa-chevron-circle-up:before {
	content: "\f139"
}
.fa-chevron-circle-down:before {
	content: "\f13a"
}
.fa-html5:before {
	content: "\f13b"
}
.fa-css3:before {
	content: "\f13c"
}
.fa-anchor:before {
	content: "\f13d"
}
.fa-unlock-alt:before {
	content: "\f13e"
}
.fa-bullseye:before {
	content: "\f140"
}
.fa-ellipsis-h:before {
	content: "\f141"
}
.fa-ellipsis-v:before {
	content: "\f142"
}
.fa-rss-square:before {
	content: "\f143"
}
.fa-play-circle:before {
	content: "\f144"
}
.fa-ticket:before {
	content: "\f145"
}
.fa-minus-square:before {
	content: "\f146"
}
.fa-minus-square-o:before {
	content: "\f147"
}
.fa-level-up:before {
	content: "\f148"
}
.fa-level-down:before {
	content: "\f149"
}
.fa-check-square:before {
	content: "\f14a"
}
.fa-pencil-square:before {
	content: "\f14b"
}
.fa-external-link-square:before {
	content: "\f14c"
}
.fa-share-square:before {
	content: "\f14d"
}
.fa-compass:before {
	content: "\f14e"
}
.fa-caret-square-o-down:before, .fa-toggle-down:before {
	content: "\f150"
}
.fa-caret-square-o-up:before, .fa-toggle-up:before {
	content: "\f151"
}
.fa-caret-square-o-right:before, .fa-toggle-right:before {
	content: "\f152"
}
.fa-eur:before, .fa-euro:before {
	content: "\f153"
}
.fa-gbp:before {
	content: "\f154"
}
.fa-dollar:before, .fa-usd:before {
	content: "\f155"
}
.fa-inr:before, .fa-rupee:before {
	content: "\f156"
}
.fa-cny:before, .fa-jpy:before, .fa-rmb:before, .fa-yen:before {
	content: "\f157"
}
.fa-rouble:before, .fa-rub:before, .fa-ruble:before {
	content: "\f158"
}
.fa-krw:before, .fa-won:before {
	content: "\f159"
}
.fa-bitcoin:before, .fa-btc:before {
	content: "\f15a"
}
.fa-file:before {
	content: "\f15b"
}
.fa-file-text:before {
	content: "\f15c"
}
.fa-sort-alpha-asc:before {
	content: "\f15d"
}
.fa-sort-alpha-desc:before {
	content: "\f15e"
}
.fa-sort-amount-asc:before {
	content: "\f160"
}
.fa-sort-amount-desc:before {
	content: "\f161"
}
.fa-sort-numeric-asc:before {
	content: "\f162"
}
.fa-sort-numeric-desc:before {
	content: "\f163"
}
.fa-thumbs-up:before {
	content: "\f164"
}
.fa-thumbs-down:before {
	content: "\f165"
}
.fa-youtube-square:before {
	content: "\f166"
}
.fa-youtube:before {
	content: "\f167"
}
.fa-xing:before {
	content: "\f168"
}
.fa-xing-square:before {
	content: "\f169"
}
.fa-youtube-play:before {
	content: "\f16a"
}
.fa-dropbox:before {
	content: "\f16b"
}
.fa-stack-overflow:before {
	content: "\f16c"
}
.fa-instagram:before {
	content: "\f16d"
}
.fa-flickr:before {
	content: "\f16e"
}
.fa-adn:before {
	content: "\f170"
}
.fa-bitbucket:before {
	content: "\f171"
}
.fa-bitbucket-square:before {
	content: "\f172"
}
.fa-tumblr:before {
	content: "\f173"
}
.fa-tumblr-square:before {
	content: "\f174"
}
.fa-long-arrow-down:before {
	content: "\f175"
}
.fa-long-arrow-up:before {
	content: "\f176"
}
.fa-long-arrow-left:before {
	content: "\f177"
}
.fa-long-arrow-right:before {
	content: "\f178"
}
.fa-apple:before {
	content: "\f179"
}
.fa-windows:before {
	content: "\f17a"
}
.fa-android:before {
	content: "\f17b"
}
.fa-linux:before {
	content: "\f17c"
}
.fa-dribbble:before {
	content: "\f17d"
}
.fa-skype:before {
	content: "\f17e"
}
.fa-foursquare:before {
	content: "\f180"
}
.fa-trello:before {
	content: "\f181"
}
.fa-female:before {
	content: "\f182"
}
.fa-male:before {
	content: "\f183"
}
.fa-gittip:before, .fa-gratipay:before {
	content: "\f184"
}
.fa-sun-o:before {
	content: "\f185"
}
.fa-moon-o:before {
	content: "\f186"
}
.fa-archive:before {
	content: "\f187"
}
.fa-bug:before {
	content: "\f188"
}
.fa-vk:before {
	content: "\f189"
}
.fa-weibo:before {
	content: "\f18a"
}
.fa-renren:before {
	content: "\f18b"
}
.fa-pagelines:before {
	content: "\f18c"
}
.fa-stack-exchange:before {
	content: "\f18d"
}
.fa-arrow-circle-o-right:before {
	content: "\f18e"
}
.fa-arrow-circle-o-left:before {
	content: "\f190"
}
.fa-caret-square-o-left:before, .fa-toggle-left:before {
	content: "\f191"
}
.fa-dot-circle-o:before {
	content: "\f192"
}
.fa-wheelchair:before {
	content: "\f193"
}
.fa-vimeo-square:before {
	content: "\f194"
}
.fa-try:before, .fa-turkish-lira:before {
	content: "\f195"
}
.fa-plus-square-o:before {
	content: "\f196"
}
.fa-space-shuttle:before {
	content: "\f197"
}
.fa-slack:before {
	content: "\f198"
}
.fa-envelope-square:before {
	content: "\f199"
}
.fa-wordpress:before {
	content: "\f19a"
}
.fa-openid:before {
	content: "\f19b"
}
.fa-bank:before, .fa-institution:before, .fa-university:before {
	content: "\f19c"
}
.fa-graduation-cap:before, .fa-mortar-board:before {
	content: "\f19d"
}
.fa-yahoo:before {
	content: "\f19e"
}
.fa-google:before {
	content: "\f1a0"
}
.fa-reddit:before {
	content: "\f1a1"
}
.fa-reddit-square:before {
	content: "\f1a2"
}
.fa-stumbleupon-circle:before {
	content: "\f1a3"
}
.fa-stumbleupon:before {
	content: "\f1a4"
}
.fa-delicious:before {
	content: "\f1a5"
}
.fa-digg:before {
	content: "\f1a6"
}
.fa-pied-piper:before {
	content: "\f1a7"
}
.fa-pied-piper-alt:before {
	content: "\f1a8"
}
.fa-drupal:before {
	content: "\f1a9"
}
.fa-joomla:before {
	content: "\f1aa"
}
.fa-language:before {
	content: "\f1ab"
}
.fa-fax:before {
	content: "\f1ac"
}
.fa-building:before {
	content: "\f1ad"
}
.fa-child:before {
	content: "\f1ae"
}
.fa-paw:before {
	content: "\f1b0"
}
.fa-spoon:before {
	content: "\f1b1"
}
.fa-cube:before {
	content: "\f1b2"
}
.fa-cubes:before {
	content: "\f1b3"
}
.fa-behance:before {
	content: "\f1b4"
}
.fa-behance-square:before {
	content: "\f1b5"
}
.fa-steam:before {
	content: "\f1b6"
}
.fa-steam-square:before {
	content: "\f1b7"
}
.fa-recycle:before {
	content: "\f1b8"
}
.fa-automobile:before, .fa-car:before {
	content: "\f1b9"
}
.fa-cab:before, .fa-taxi:before {
	content: "\f1ba"
}
.fa-tree:before {
	content: "\f1bb"
}
.fa-spotify:before {
	content: "\f1bc"
}
.fa-deviantart:before {
	content: "\f1bd"
}
.fa-soundcloud:before {
	content: "\f1be"
}
.fa-database:before {
	content: "\f1c0"
}
.fa-file-pdf-o:before {
	content: "\f1c1"
}
.fa-file-word-o:before {
	content: "\f1c2"
}
.fa-file-excel-o:before {
	content: "\f1c3"
}
.fa-file-powerpoint-o:before {
	content: "\f1c4"
}
.fa-file-image-o:before, .fa-file-photo-o:before, .fa-file-picture-o:before {
	content: "\f1c5"
}
.fa-file-archive-o:before, .fa-file-zip-o:before {
	content: "\f1c6"
}
.fa-file-audio-o:before, .fa-file-sound-o:before {
	content: "\f1c7"
}
.fa-file-movie-o:before, .fa-file-video-o:before {
	content: "\f1c8"
}
.fa-file-code-o:before {
	content: "\f1c9"
}
.fa-vine:before {
	content: "\f1ca"
}
.fa-codepen:before {
	content: "\f1cb"
}
.fa-jsfiddle:before {
	content: "\f1cc"
}
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-ring:before, .fa-life-saver:before, .fa-support:before {
	content: "\f1cd"
}
.fa-circle-o-notch:before {
	content: "\f1ce"
}
.fa-ra:before, .fa-rebel:before {
	content: "\f1d0"
}
.fa-empire:before, .fa-ge:before {
	content: "\f1d1"
}
.fa-git-square:before {
	content: "\f1d2"
}
.fa-git:before {
	content: "\f1d3"
}
.fa-hacker-news:before, .fa-y-combinator-square:before, .fa-yc-square:before {
	content: "\f1d4"
}
.fa-tencent-weibo:before {
	content: "\f1d5"
}
.fa-qq:before {
	content: "\f1d6"
}
.fa-wechat:before, .fa-weixin:before {
	content: "\f1d7"
}
.fa-paper-plane:before, .fa-send:before {
	content: "\f1d8"
}
.fa-paper-plane-o:before, .fa-send-o:before {
	content: "\f1d9"
}
.fa-history:before {
	content: "\f1da"
}
.fa-circle-thin:before {
	content: "\f1db"
}
.fa-header:before {
	content: "\f1dc"
}
.fa-paragraph:before {
	content: "\f1dd"
}
.fa-sliders:before {
	content: "\f1de"
}
.fa-share-alt:before {
	content: "\f1e0"
}
.fa-share-alt-square:before {
	content: "\f1e1"
}
.fa-bomb:before {
	content: "\f1e2"
}
.fa-futbol-o:before, .fa-soccer-ball-o:before {
	content: "\f1e3"
}
.fa-tty:before {
	content: "\f1e4"
}
.fa-binoculars:before {
	content: "\f1e5"
}
.fa-plug:before {
	content: "\f1e6"
}
.fa-slideshare:before {
	content: "\f1e7"
}
.fa-twitch:before {
	content: "\f1e8"
}
.fa-yelp:before {
	content: "\f1e9"
}
.fa-newspaper-o:before {
	content: "\f1ea"
}
.fa-wifi:before {
	content: "\f1eb"
}
.fa-calculator:before {
	content: "\f1ec"
}
.fa-paypal:before {
	content: "\f1ed"
}
.fa-google-wallet:before {
	content: "\f1ee"
}
.fa-cc-visa:before {
	content: "\f1f0"
}
.fa-cc-mastercard:before {
	content: "\f1f1"
}
.fa-cc-discover:before {
	content: "\f1f2"
}
.fa-cc-amex:before {
	content: "\f1f3"
}
.fa-cc-paypal:before {
	content: "\f1f4"
}
.fa-cc-stripe:before {
	content: "\f1f5"
}
.fa-bell-slash:before {
	content: "\f1f6"
}
.fa-bell-slash-o:before {
	content: "\f1f7"
}
.fa-trash:before {
	content: "\f1f8"
}
.fa-copyright:before {
	content: "\f1f9"
}
.fa-at:before {
	content: "\f1fa"
}
.fa-eyedropper:before {
	content: "\f1fb"
}
.fa-paint-brush:before {
	content: "\f1fc"
}
.fa-birthday-cake:before {
	content: "\f1fd"
}
.fa-area-chart:before {
	content: "\f1fe"
}
.fa-pie-chart:before {
	content: "\f200"
}
.fa-line-chart:before {
	content: "\f201"
}
.fa-lastfm:before {
	content: "\f202"
}
.fa-lastfm-square:before {
	content: "\f203"
}
.fa-toggle-off:before {
	content: "\f204"
}
.fa-toggle-on:before {
	content: "\f205"
}
.fa-bicycle:before {
	content: "\f206"
}
.fa-bus:before {
	content: "\f207"
}
.fa-ioxhost:before {
	content: "\f208"
}
.fa-angellist:before {
	content: "\f209"
}
.fa-cc:before {
	content: "\f20a"
}
.fa-ils:before, .fa-shekel:before, .fa-sheqel:before {
	content: "\f20b"
}
.fa-meanpath:before {
	content: "\f20c"
}
.fa-buysellads:before {
	content: "\f20d"
}
.fa-connectdevelop:before {
	content: "\f20e"
}
.fa-dashcube:before {
	content: "\f210"
}
.fa-forumbee:before {
	content: "\f211"
}
.fa-leanpub:before {
	content: "\f212"
}
.fa-sellsy:before {
	content: "\f213"
}
.fa-shirtsinbulk:before {
	content: "\f214"
}
.fa-simplybuilt:before {
	content: "\f215"
}
.fa-skyatlas:before {
	content: "\f216"
}
.fa-cart-plus:before {
	content: "\f217"
}
.fa-cart-arrow-down:before {
	content: "\f218"
}
.fa-diamond:before {
	content: "\f219"
}
.fa-ship:before {
	content: "\f21a"
}
.fa-user-secret:before {
	content: "\f21b"
}
.fa-motorcycle:before {
	content: "\f21c"
}
.fa-street-view:before {
	content: "\f21d"
}
.fa-heartbeat:before {
	content: "\f21e"
}
.fa-venus:before {
	content: "\f221"
}
.fa-mars:before {
	content: "\f222"
}
.fa-mercury:before {
	content: "\f223"
}
.fa-intersex:before, .fa-transgender:before {
	content: "\f224"
}
.fa-transgender-alt:before {
	content: "\f225"
}
.fa-venus-double:before {
	content: "\f226"
}
.fa-mars-double:before {
	content: "\f227"
}
.fa-venus-mars:before {
	content: "\f228"
}
.fa-mars-stroke:before {
	content: "\f229"
}
.fa-mars-stroke-v:before {
	content: "\f22a"
}
.fa-mars-stroke-h:before {
	content: "\f22b"
}
.fa-neuter:before {
	content: "\f22c"
}
.fa-genderless:before {
	content: "\f22d"
}
.fa-facebook-official:before {
	content: "\f230"
}
.fa-pinterest-p:before {
	content: "\f231"
}
.fa-whatsapp:before {
	content: "\f232"
}
.fa-server:before {
	content: "\f233"
}
.fa-user-plus:before {
	content: "\f234"
}
.fa-user-times:before {
	content: "\f235"
}
.fa-bed:before, .fa-hotel:before {
	content: "\f236"
}
.fa-viacoin:before {
	content: "\f237"
}
.fa-train:before {
	content: "\f238"
}
.fa-subway:before {
	content: "\f239"
}
.fa-medium:before {
	content: "\f23a"
}
.fa-y-combinator:before, .fa-yc:before {
	content: "\f23b"
}
.fa-optin-monster:before {
	content: "\f23c"
}
.fa-opencart:before {
	content: "\f23d"
}
.fa-expeditedssl:before {
	content: "\f23e"
}
.fa-battery-4:before, .fa-battery-full:before {
	content: "\f240"
}
.fa-battery-3:before, .fa-battery-three-quarters:before {
	content: "\f241"
}
.fa-battery-2:before, .fa-battery-half:before {
	content: "\f242"
}
.fa-battery-1:before, .fa-battery-quarter:before {
	content: "\f243"
}
.fa-battery-0:before, .fa-battery-empty:before {
	content: "\f244"
}
.fa-mouse-pointer:before {
	content: "\f245"
}
.fa-i-cursor:before {
	content: "\f246"
}
.fa-object-group:before {
	content: "\f247"
}
.fa-object-ungroup:before {
	content: "\f248"
}
.fa-sticky-note:before {
	content: "\f249"
}
.fa-sticky-note-o:before {
	content: "\f24a"
}
.fa-cc-jcb:before {
	content: "\f24b"
}
.fa-cc-diners-club:before {
	content: "\f24c"
}
.fa-clone:before {
	content: "\f24d"
}
.fa-balance-scale:before {
	content: "\f24e"
}
.fa-hourglass-o:before {
	content: "\f250"
}
.fa-hourglass-1:before, .fa-hourglass-start:before {
	content: "\f251"
}
.fa-hourglass-2:before, .fa-hourglass-half:before {
	content: "\f252"
}
.fa-hourglass-3:before, .fa-hourglass-end:before {
	content: "\f253"
}
.fa-hourglass:before {
	content: "\f254"
}
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
	content: "\f255"
}
.fa-hand-paper-o:before, .fa-hand-stop-o:before {
	content: "\f256"
}
.fa-hand-scissors-o:before {
	content: "\f257"
}
.fa-hand-lizard-o:before {
	content: "\f258"
}
.fa-hand-spock-o:before {
	content: "\f259"
}
.fa-hand-pointer-o:before {
	content: "\f25a"
}
.fa-hand-peace-o:before {
	content: "\f25b"
}
.fa-trademark:before {
	content: "\f25c"
}
.fa-registered:before {
	content: "\f25d"
}
.fa-creative-commons:before {
	content: "\f25e"
}
.fa-gg:before {
	content: "\f260"
}
.fa-gg-circle:before {
	content: "\f261"
}
.fa-tripadvisor:before {
	content: "\f262"
}
.fa-odnoklassniki:before {
	content: "\f263"
}
.fa-odnoklassniki-square:before {
	content: "\f264"
}
.fa-get-pocket:before {
	content: "\f265"
}
.fa-wikipedia-w:before {
	content: "\f266"
}
.fa-safari:before {
	content: "\f267"
}
.fa-chrome:before {
	content: "\f268"
}
.fa-firefox:before {
	content: "\f269"
}
.fa-opera:before {
	content: "\f26a"
}
.fa-internet-explorer:before {
	content: "\f26b"
}
.fa-television:before, .fa-tv:before {
	content: "\f26c"
}
.fa-contao:before {
	content: "\f26d"
}
.fa-500px:before {
	content: "\f26e"
}
.fa-amazon:before {
	content: "\f270"
}
.fa-calendar-plus-o:before {
	content: "\f271"
}
.fa-calendar-minus-o:before {
	content: "\f272"
}
.fa-calendar-times-o:before {
	content: "\f273"
}
.fa-calendar-check-o:before {
	content: "\f274"
}
.fa-industry:before {
	content: "\f275"
}
.fa-map-pin:before {
	content: "\f276"
}
.fa-map-signs:before {
	content: "\f277"
}
.fa-map-o:before {
	content: "\f278"
}
.fa-map:before {
	content: "\f279"
}
.fa-commenting:before {
	content: "\f27a"
}
.fa-commenting-o:before {
	content: "\f27b"
}
.fa-houzz:before {
	content: "\f27c"
}
.fa-vimeo:before {
	content: "\f27d"
}
.fa-black-tie:before {
	content: "\f27e"
}
.fa-fonticons:before {
	content: "\f280"
}
.fa-reddit-alien:before {
	content: "\f281"
}
.fa-edge:before {
	content: "\f282"
}
.fa-credit-card-alt:before {
	content: "\f283"
}
.fa-codiepie:before {
	content: "\f284"
}
.fa-modx:before {
	content: "\f285"
}
.fa-fort-awesome:before {
	content: "\f286"
}
.fa-usb:before {
	content: "\f287"
}
.fa-product-hunt:before {
	content: "\f288"
}
.fa-mixcloud:before {
	content: "\f289"
}
.fa-scribd:before {
	content: "\f28a"
}
.fa-pause-circle:before {
	content: "\f28b"
}
.fa-pause-circle-o:before {
	content: "\f28c"
}
.fa-stop-circle:before {
	content: "\f28d"
}
.fa-stop-circle-o:before {
	content: "\f28e"
}
.fa-shopping-bag:before {
	content: "\f290"
}
.fa-shopping-basket:before {
	content: "\f291"
}
.fa-hashtag:before {
	content: "\f292"
}
.fa-bluetooth:before {
	content: "\f293"
}
.fa-bluetooth-b:before {
	content: "\f294"
}
.fa-percent:before {
	content: "\f295"
}
code[class*=language-], pre[class*=language-] {
	color: #000;
	text-shadow: 0 1px #fff;
	font-family: Consolas, Monaco, Andale Mono, monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;
	-moz-tab-size: 4;
	tab-size: 4;
	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none
}
code[class*=language-] ::-moz-selection, code[class*=language-]::-moz-selection, pre[class*=language-] ::-moz-selection, pre[class*=language-]::-moz-selection {
text-shadow:none;
background:#b3d4fc
}
code[class*=language-] ::selection, code[class*=language-]::selection, pre[class*=language-] ::selection, pre[class*=language-]::selection {
	text-shadow: none;
	background: #b3d4fc
}
@media print {
code[class*=language-], pre[class*=language-] {
	text-shadow: none
}
}
pre[class*=language-] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto
}
:not(pre)>code[class*=language-], pre[class*=language-] {
	background: #f5f2f0
}
:not(pre)>code[class*=language-] {
	padding: .1em;
	border-radius: .3em
}
.token.cdata, .token.comment, .token.doctype, .token.prolog {
	color: #708090
}
.token.punctuation {
	color: #999
}
.namespace {
	opacity: .7
}
.token.boolean, .token.constant, .token.deleted, .token.number, .token.property, .token.symbol, .token.tag {
	color: #905
}
.token.attr-name, .token.builtin, .token.char, .token.inserted, .token.selector, .token.string {
	color: #690
}
.language-css .token.string, .style .token.string, .token.entity, .token.operator, .token.url {
	color: #a67f59;
	background: hsla(0,0%,100%,.5)
}
.token.atrule, .token.attr-value, .token.keyword {
	color: #07a
}
.token.function {
	color: #dd4a68
}
.token.important, .token.regex, .token.variable {
	color: #e90
}
.token.bold, .token.important {
	font-weight: 700
}
.token.italic {
	font-style: italic
}
.token.entity {
	cursor: help
}
pre.line-numbers {
	padding-left: 3.8em;
	counter-reset: a
}
pre.line-numbers, pre.line-numbers>code {
	position: relative
}
.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em;
	letter-spacing: -1px;
	border-right: 1px solid #999;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}
.line-numbers-rows>span {
	pointer-events: none;
	display: block;
	counter-increment: a
}
.line-numbers-rows>span:before {
	content: counter(a);
	color: #999;
	display: block;
	padding-right: .8em;
	text-align: right
}
pre[class*=language-] {
	position: relative
}
pre[class*=language-][data-language]:before {
	content: attr(data-language);
	color: #000;
	background-color: #cfcfcf;
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	font-size: .9em;
	border-radius: 0 0 0 5px;
	padding: 0 .5em;
	text-shadow: none
}
/*! Lity - v1.5.1 - 2015-12-02
* http://sorgalla.com/lity/
* Copyright (c) 2015 Jan Sorgalla; Licensed MIT */

.lity {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	white-space: nowrap;
	background: rgba(0,0,0,.9);
	outline: none!important;
	opacity: 0;
	transition: opacity .3s ease;
	z-index: 9;
}
.lity * {
	box-sizing: border-box;
}
.lity-wrap {
	z-index: 16;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	text-align: center;
	outline: none!important;
}
.lity-wrap:before {
	content: '';
	display: inline-block;
	height: 100%;
	vertical-align: middle;
	margin-right: -.25em;
}
.lity-loader {
	z-index: 17;
	color: #fff;
	position: absolute;
	top: 50%;
	margin-top: -.8em;
	width: 100%;
	text-align: center;
	font-size: 14px;
	font-family: Arial, Helvetica, sans-serif;
	opacity: 0;
	transition: opacity .3s ease;
}
.lity-container {
	z-index: 18;
	position: relative;
	text-align: left;
	vertical-align: middle;
	display: inline-block;
	white-space: normal;
	max-width: 100%;
	max-height: 100%;
	outline: none!important;
}
.lity-content {
	z-index: 19;
	width: 100%;
	-webkit-transform: scale(1);
	transform: scale(1);
	transition: transform .3s ease;
}
.lity-closed .lity-content, .lity-loading .lity-content {
	-webkit-transform: scale(.8);
	transform: scale(.8);
}
.lity-content:after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	display: block;
	right: 0;
	width: auto;
	height: auto;
	z-index: -1;
	box-shadow: 0 0 8px rgba(0,0,0,.6);
}
.lity-close {
	z-index: 20;
	width: 35px;
	height: 35px;
	position: fixed;
	right: 0;
	top: 0;
	-webkit-appearance: none;
	cursor: pointer;
	text-decoration: none;
	text-align: center;
	color: #fff;
	font-style: normal;
	font-size: 35px;
	font-family: Arial, Baskerville, monospace;
	line-height: 35px;
	text-shadow: 0 1px 2px rgba(0,0,0,.6);
	border: 0;
	background: none;
	outline: none;
	box-shadow: none;
	padding: 0;
}
 .lity-close::-moz-focus-inner {
border:0;
padding:0;
}
.lity-close:active, .lity-close:focus, .lity-close:hover, .lity-close:visited {
	text-decoration: none;
	text-align: center;
	color: #fff;
	font-style: normal;
	font-size: 35px;
	font-family: Arial, Baskerville, monospace;
	line-height: 35px;
	text-shadow: 0 1px 2px rgba(0,0,0,.6);
	border: 0;
	background: none;
	outline: none;
	box-shadow: none;
	padding: 0;
}
.lity-close:active {
	top: 1px;
}
.lity-image img {
	max-width: 100%;
	display: block;
	line-height: 0;
	border: 0;
}
.lity-iframe .lity-container {
	width: 100%;
	max-width: 964px;
}
.lity-iframe-container {
	width: 100%;
	height: 0;
	overflow: hidden;
	padding-top: 56.25%;
}
.lity-iframe-container iframe {
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	box-shadow: 0 0 8px rgba(0,0,0,.6);
	background: #000;
}
.main-content {
	font-size: 16px;
	line-height: 27px;
	padding: 50px 50px 50px 0;
}
.main-content.col-md-12 {
	padding-right: 0;
}
.main-content>header {
	margin-bottom: 20px;
	border-bottom: 1px solid #f1f1f1;
}
.main-content>header>p:last-child {
	margin-bottom: 30px;
}
.main-content>section {
	padding-bottom: 30px;
}
.main-content>section:last-child {
	border-bottom: 0;
	padding-bottom: 0;
}
.sidebar+.main-content {
	padding-left: 50px;
	padding-right: 0;
}
.container-fluid .sidebar+.main-content {
	padding-right: 30px;
}
.container-fluid .main-content.col-md-12 {
	padding-left: 30px;
	padding-right: 30px;
}
.site-header .navbar {
	background-color: #5cc7b2;
	border: none;
	border-radius: 0;
	transition: .2s ease;
	margin: 0;
	padding: 20px 15px;
}
.site-header .navbar-brand {
	display: inline-block;
	padding-top: 0;
	padding-bottom: 0;
	float: none;
}
.site-header .navbar-brand>img {
	display: inline-block;
	max-height: 36px;
	transition: .2s ease;
}
.site-header .navbar-brand .force-middle {
	display: inline-block;
	vertical-align: middle;
	height: 100%;
}
.site-header.navbar-sm .navbar {
	padding-top: 8px;
	padding-bottom: 8px;
}
.site-header.navbar-sm .navbar-brand>img {
	max-height: 24px;
}
.site-header.navbar-lg .navbar {
	padding-top: 32px;
	padding-bottom: 32px;
}
.site-header.navbar-lg .navbar-brand>img {
	max-height: 48px;
}
.site-header.sticky .navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 7;
	box-shadow: 0 0 10px 0 rgba(0,0,0,.2);
}
.site-header.navbar-fullwidth .navbar .container {
	width: 100%;
	margin: 0;
}
.site-header.navbar-transparent {
	padding-top: 0;
}
.site-header.navbar-transparent .navbar {
	background-color: transparent;
	box-shadow: none!important;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 7;
}
.navbar-default .navbar-brand, .navbar-default .navbar-brand:focus, .navbar-default .navbar-brand:hover {
	color: #fff;
	font-size: 25px;
	font-weight: 300;
}
.navbar-default .navbar-nav>li>a {
	color: hsla(0,0%,100%,.8);
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 1px;
	transition: .2s ease;
}
.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: transparent;
	font-weight: 700;
}
.navbar-default .navbar-nav>.open>a {
	color: #fff;
	background-color: transparent;
	transition: none;
}
.navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover {
	color: #777;
	background-color: #fff;
}
.navbar-default .navbar-nav>.hero>a {
	background-color: hsla(0,0%,100%,.1);
	border-radius: 3px;
	transition: .3s linear;
}
.navbar-default .navbar-nav>.hero>a:focus, .navbar-default .navbar-nav>.hero>a:hover {
	color: #fff;
	background-color: hsla(0,0%,100%,.25);
}
.nav .dropdown-menu {
	box-shadow: none;
	border-top: none;
	border-color: #e7e7e7;
}
.nav .dropdown-menu>li>a {
	padding: 8px 20px;
}
.nav .dropdown-menu>li>a:focus, .nav .dropdown-menu>li>a:hover {
	background-color: #f5f5f5;
}
.navbar-default .navbar-toggle {
	border: none;
	border-radius: 2px;
	color: hsla(0,0%,100%,.8);
	font-size: 20px;
	margin: 0;
	padding: 7px 11px;
}
.navbar-default .navbar-toggle:focus, .navbar-default .navbar-toggle:hover {
	color: #fff;
	background-color: hsla(0,0%,100%,.2);
}
.navbar-default .navbar-toggle:focus .icon-bar, .navbar-default .navbar-toggle:hover .icon-bar {
	background-color: #fff;
}
.navbar-default .navbar-toggle.for-sidebar {
	float: left;
	margin-top: 3px;
	padding: 14px 10px;
}
.navbar-default .navbar-toggle .icon-bar {
	background-color: hsla(0,0%,100%,.8);
}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
	border-color: hsla(0,0%,100%,.1);
}
.navbar-default.navbar-light {
	background-color: #fff;
	box-shadow: 0 0 10px 0 rgba(0,0,0,.2);
}
.navbar-default.navbar-light .open>a {
	color: #5cc7b2;
	background-color: #fcfcfc!important;
}
.navbar-default.navbar-light .hero>a {
	background-color: rgba(92,199,178,.8);
	color: #fff;
}
.navbar-default.navbar-light .hero>a:focus, .navbar-default.navbar-light .hero>a:hover {
	color: #fff;
	background-color: rgba(92,199,178,.99);
}
.navbar-default.navbar-dark {
	background-color: #292929;
}
.site-footer {
	background-color: #f5f5f5;
	color: #777;
	padding: 30px 15px;
}
.site-footer div>p:last-child {
	margin-bottom: 0;
}
.site-footer #scroll-up {
	position: absolute;
	top: -47px;
	right: 0;
	color: #ccc;
	background-color: #f5f5f5;
	display: inline-block;
	width: 34px;
	height: 34px;
	border-radius: 100%;
	text-align: center;
	font-size: 21px;
	transition: .2s ease;
}
.site-footer #scroll-up:hover {
	color: #999;
}
.footer-menu {
	list-style: none;
	text-align: right;
	margin: 0;
	padding: 0;
}
.footer-menu li {
	display: inline-block;
	padding: 0 8px;
}
.footer-menu a {
	color: hsla(0,0%,49%,.7);
	font-weight: 500;
	transition: .2s ease;
}
.footer-menu a.active, .footer-menu a:hover {
	color: #7d7d7d;
	text-decoration: none;
}
.sidebar {
	padding: 50px 20px 20px;
}
.sidenav {
	min-width: 180px;
}
.sidenav, .sidenav ul {
	list-style: none;
	padding-left: 0;
}
.sidenav ul {
	padding-left: 24px;
}
.sidenav a {
	display: block;
	line-height: 30px;
	color: #777;
	transition: .3s ease;
}
.sidenav a.active, .sidenav a:hover {
	text-decoration: none;
	opacity: 1;
	color: #5cc7b2;
}
.sidenav>li>a {
	font-size: 16px;
	line-height: 30px;
}
.sidenav>li {
	position: relative;
	font-size: 16px;
	line-height: 36px;
	color: #777;
}
.sidenav>li>ul {
	padding-bottom: 20px;
}
.sidenav>li>ul:before {
	content: '';
	position: absolute;
	top: 36px;
	bottom: 20px;
	left: 0;
	border-left: 1px solid #ccc;
}
.sidenav>li>ul a.active:before, .sidenav>li>ul li.active a:before {
	content: ' ';
	position: absolute;
	left: -26px;
	top: 12px;
	width: 6px;
	height: 6px;
	border-radius: 100%;
	background-color: #5cc7b2;
}
.sidenav>li li {
	font-size: 14px;
	line-height: 30px;
}
.sidenav .fa, .sidenav .glyphicon {
	margin-right: 4px;
}
.sidebar-line .sidenav a.active:before, .sidebar-line .sidenav li.active>a:before {
	content: ' ';
	position: absolute;
	left: -10px;
	top: 5px;
	bottom: 5px;
	width: 1px;
	height: 20px;
	border-radius: 0;
	background-color: #5cc7b2;
}
.sidebar-line .sidenav>li.active>a:before, .sidebar-line .sidenav>li>a.active:before {
	height: 25px;
	width: 2px;
}
.sidebar-boxed {
	background-color: #f5f5f5;
	padding-left: 0;
	padding-right: 0;
	width: 280px;
	padding-top: 100px;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	overflow-y: auto;
}
.sidebar-boxed~footer, .sidebar-boxed~header, .sidebar-boxed~main {
	padding-right: 280px;
}
.sidebar-boxed~header.navbar-transparent .navbar {
	right: 0;
	left: 280px;
}
.sidebar-boxed~footer .row, .sidebar-boxed~header .banner {
	padding-left: 60px;
	padding-right: 60px;
}
.sidebar-boxed~main .main-content {
	padding-left: 60px;
	padding-right: 75px;
}
.sidebar-boxed.sidebar-right {
	left: auto;
	right: 0;
}
.sidebar-boxed.sidebar-right~footer, .sidebar-boxed.sidebar-right~header, .sidebar-boxed.sidebar-right~main {
	padding-left: 0;
	padding-right: 280px;
}
.sidebar-boxed.sidebar-right~header.navbar-transparent .navbar {
	right: 280px;
	left: 0;
}
.sidebar-boxed .sidebar-brand {
	margin-top: -50px;
	margin-bottom: 50px;
	display: block;
	text-align: center;
}
.sidebar-boxed .sidenav a {
	padding: 5px 20px!important;
}
.sidebar-boxed .sidenav>li {
	position: relative;
}
.sidebar-boxed .sidenav>li>a {
	padding-top: 8px!important;
	padding-bottom: 8px!important;
	color: #777;
}
.sidebar-boxed .sidenav>li>a.has-child:after {
	content: "\f105";
	font-family: FontAwesome;
	position: absolute;
	right: 20px;
	top: 4px;
}
.sidebar-boxed .sidenav>li.active>a.has-child:after, .sidebar-boxed .sidenav>li>a.open:after {
	content: "\f107";
}
.sidebar-boxed .sidenav ul {
	padding-left: 0;
}
.sidebar-boxed .sidenav ul a {
	padding-left: 44px!important;
}
.sidebar-boxed.sidebar-dark {
	background-color: #f9f9f9;
}
.sidebar-boxed.sidebar-dark .sidenav a {
	color: #000000;
	font-weight: 600;
}
.sidebar-boxed.sidebar-dark .sidenav a.open, .sidebar-boxed.sidebar-dark .sidenav a.open+ul, .sidebar-boxed.sidebar-dark .sidenav>li>a:hover {
	background-color: #7f00ff;
	color: #fff;
}
body {
	font-family: Lato, Helvetica Neue, Helvetica, Arial, sans-serif;
	color: #333;
}
hr {
	margin-top: 40px;
	margin-bottom: 40px;
}
h1 {
	font-weight: 200;
	font-size: 52px;
	line-height: 52px;
	margin-bottom: 45px;
}
h2 {
	font-size: 25px;
	line-height: 33px;
	margin-bottom: 35px;
	margin-top: 65px;
	font-weight: 300;
	padding-bottom: 12px;
	border-bottom: 1px solid #ddd;
}
h2 a {
	position: relative;
	line-height: 44px;
}
h2 a:after {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: -20px;
	height: 1px;
	background-color: #aaa;
}
h3 {
	font-size: 25px;
	line-height: 30px;
	margin-bottom: 28px;
	margin-top: 45px;
	font-weight: 300;
}
h4 {
	font-size: 25px;
}
h5 {
	font-size: 21px;
}
h6 {
	font-size: 18px;
	font-weight: 400;
}
h1, h2, h3, h4, h5, h6 {
	font-family: Raleway, Helvetica Neue, Helvetica, Arial, sans-serif;
	font-weight: 600;
}
h1 a, h1 a:focus, h1 a:hover, h2 a, h2 a:focus, h2 a:hover, h3 a, h3 a:focus, h3 a:hover, h4 a, h4 a:focus, h4 a:hover, h5 a, h5 a:focus, h5 a:hover, h6 a, h6 a:focus, h6 a:hover {
	color: #000;
	text-decoration: none;
}
h1 a:before, h2 a:before, h3 a:before {
	margin-left: -30px;
	margin-right: 8px;
	font-family: Glyphicons Halflings;
	font-size: 22px;
	color: #5cc7b2;
	opacity: .3;
	content: "\e144";
	transition: .3s ease;
}
h1:hover a:before, h2:hover a:before, h3:hover a:before {
	opacity: .6;
}
.release-date {
	margin-left: 24px;
	font-size: 15px;
	font-weight: 500;
	color: #d5d5d5;
}
.release-date:before {
	content: "\f133";
	font-family: FontAwesome;
	margin-right: 8px;
}
.text-primary {
	color: #2196f3;
}
.text-success {
	color: #4caf50;
}
.text-info {
	color: #29b6f6;
}
.text-warning {
	color: #ff9800;
}
.text-danger {
	color: #f44336;
}
.text-purple {
	color: #6d5cae;
}
.text-teal {
	color: #00bfa5;
}
.text-dark {
	color: #424242;
}
.label {
	font-weight: 400;
	padding: .3em .6em;
}
.label .fa, .label .glyphicon {
	padding: 0 4px;
}
.alert {
	border-radius: 2px;
}
.callout {
	border: 1px solid #eee;
	border-left-width: 3px;
	border-radius: 3px;
	margin: 20px 0;
	padding: 12px 20px;
}
.callout h4 {
	font-size: 20px;
}
.callout-success {
	border-left-color: #10cfbd;
}
.callout-success a, .callout-success h4 {
	color: #10cfbd;
}
.callout-info {
	border-left-color: #48b0f7;
}
.callout-info a, .callout-info h4 {
	color: #48b0f7;
}
.callout-warning {
	border-left-color: #f8d053;
}
.callout-warning a, .callout-warning h4 {
	color: #f8d053;
}
.callout-danger {
	border-left-color: #f55753;
}
.callout-danger a, .callout-danger h4 {
	color: #f55753;
}
.btn {
	border-radius: 2px;
	border: none;
	transition: .3s ease;
}
.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
	outline: none;
}
.btn-lg {
	border-radius: 4px;
}
.btn-xl {
	font-size: 20px;
	line-height: 1.6666666;
	border-radius: 6px;
	padding: 14px 24px;
}
.btn-default {
	background-color: #f5f5f5;
	color: #777;
}
.btn-white {
	background-color: #fff;
	color: #777;
}
.btn-primary.active, .btn-primary.focus, .btn-primary:active, .btn-primary:focus, .btn-primary:hover {
	background-color: #1e88e5;
}
.btn-success.active, .btn-success.focus, .btn-success:active, .btn-success:focus, .btn-success:hover {
	background-color: #43a047;
}
.btn-info.active, .btn-info.focus, .btn-info:active, .btn-info:focus, .btn-info:hover {
	background-color: #039be5;
}
.btn-warning.active, .btn-warning.focus, .btn-warning:active, .btn-warning:focus, .btn-warning:hover {
	background-color: #f57c00;
}
.btn-danger.active, .btn-danger.focus, .btn-danger:active, .btn-danger:focus, .btn-danger:hover {
	background-color: #d32f2f;
}
.btn-purple.active, .btn-purple.focus, .btn-purple:active, .btn-purple:focus, .btn-purple:hover {
	background-color: #554790;
}
.btn-teal.active, .btn-teal.focus, .btn-teal:active, .btn-teal:focus, .btn-teal:hover {
	background-color: #00af97;
}
.btn-dark.active, .btn-dark.focus, .btn-dark:active, .btn-dark:focus, .btn-dark:hover {
	background-color: #000;
}
.btn-white.active, .btn-white.focus, .btn-white:active, .btn-white:focus, .btn-white:hover {
	background-color: #fff;
	color: #424242!important;
}
.btn-outline {
	background-color: transparent;
	border: 1px solid transparent;
	border-radius: 3px;
}
.btn-outline.btn-white.active, .btn-outline.btn-white.focus, .btn-outline.btn-white:active, .btn-outline.btn-white:focus, .btn-outline.btn-white:hover {
	color: #333!important;
}
.btn-round {
	background-color: transparent;
	border: 1px solid transparent;
	border-radius: 17px;
	padding: 6px 16px;
}
.btn-round.btn-xs {
	border-radius: 11px;
	padding: 1px 8px;
}
.btn-round.btn-sm {
	border-radius: 15px;
	padding: 6px 12px;
}
.btn-round.btn-lg {
	border-radius: 23px;
	padding: 10px 20px;
}
.table-info tr>td:first-child {
	font-weight: 400;
	padding-right: 10px;
}
.table-changelog thead>tr>th:first-child, .table-changelog tr>td:first-child {
	width: 100px;
}
.table-changelog>tbody>tr>td, .table-changelog>tbody>tr>th, .table-changelog>tfoot>tr>td, .table-changelog>tfoot>tr>th, .table-changelog>thead>tr>td, .table-changelog>thead>tr>th {
	border-top-width: 0;
}
.table-bordered.table-changelog>tbody>tr>td, .table-bordered.table-changelog>tbody>tr>th, .table-bordered.table-changelog>tfoot>tr>td, .table-bordered.table-changelog>tfoot>tr>th, .table-bordered.table-changelog>thead>tr>td, .table-bordered.table-changelog>thead>tr>th {
	border-top-width: 1px;
}
.change {
	display: inline-block;
	width: 70px;
	font-size: 12px;
	font-weight: 500;
	line-height: 1;
	color: #fff;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: .25em;
	padding: 7px 0;
}
.change-added:after {
	content: "Added";
}
.change-fixed:after {
	content: "Fixed";
}
.change-removed:after {
	content: "Removed";
}
.change-updated:after {
	content: "Updated";
}
.change-moved:after {
	content: "Moved";
}
.change-merged:after {
	content: "Merged";
}
.changelog {
	padding-left: 0;
	list-style: none;
	margin-left: 24px;
	margin-right: 24px;
}
.changelog>li {
	display: block;
	position: relative;
	padding-left: 96px;
	margin-bottom: 12px;
}
.changelog>li:before {
	content: "";
	display: inline-block;
	width: 70px;
	font-size: 12px;
	font-weight: 500;
	line-height: 1;
	color: #fff;
	letter-spacing: 1px;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: .25em;
	position: absolute;
	left: 0;
	top: 2px;
	padding: 7px 0;
}
.changelog .ch-added:before {
	content: "Added";
	background-color: #4caf50;
}
.changelog .ch-fixed:before {
	content: "Fixed";
	background-color: #ff9800;
}
.changelog .ch-removed:before {
	content: "Removed";
	background-color: #f44336;
}
.changelog .ch-updated:before {
	content: "Updated";
	background-color: #29b6f6;
}
.changelog .ch-moved:before {
	content: "Moved";
	background-color: #6d5cae;
}
.changelog .ch-merged:before {
	content: "Merged";
	background-color: #00bfa5;
}
.toc {
	position: relative;
	list-style: none;
	margin-top: 40px;
	margin-bottom: 0;
	line-height: 30px;
	background-color: #fcfcfc;
	counter-reset: b;
	padding: 50px 20px 20px 30px;
}
.toc a {
	color: #888;
	transition: .3s ease;
}
.toc a:active, .toc a:hover {
	text-decoration: none;
	color: #5cc7b2;
}
.toc>li:before {
	content: counter(b) ". ";
	counter-increment: b;
}
.toc>li>ol {
	counter-reset: c;
}
.toc>li>ol>li:before {
	content: counter(b) "." counter(c) ". ";
	counter-increment: c;
}
.toc>li>ol>li>ol {
	counter-reset: d;
}
.toc>li>ol>li>ol>li:before {
	content: counter(b) "." counter(c) ". " counter(d) ". ";
	counter-increment: d;
}
.toc ol {
	list-style: none;
	padding-left: 25px;
	line-height: 23px;
}
.toc ol li:last-child {
	margin-bottom: 10px;
}
.toc ol li:before, .toc>li:before {
	color: #888;
	font-weight: 500;
	font-size: 14px;
	margin-right: 5px;
}
.toc:before {
	position: absolute;
	top: 15px;
	left: 15px;
	font-size: 12px;
	font-weight: 600;
	content: "Table of content";
}
.faq>h5, .toc:before {
	letter-spacing: 1px;
	text-transform: uppercase;
	color: #ccc;
}
.faq>h5 {
	font-size: 15px;
	font-weight: 500;
	margin-bottom: 30px;
}
.faq>ul {
	list-style: none;
	margin-bottom: 50px;
	margin-left: 20px;
	padding: 0;
}
.faq>ul>li {
	margin-bottom: 20px;
	padding-bottom: 10px;
	border-bottom: 1px solid #e7e7e7;
}
.faq>ul>li:last-child {
	border-bottom: 0;
	margin-bottom: 0;
	padding-bottom: 0;
}
.faq li h6 {
	font-size: 20px;
	font-weight: 400;
	cursor: pointer;
}
.faq li h6:before {
	margin-right: 12px;
	display: inline-block;
	width: 20px;
	vertical-align: text-top;
	font-family: FontAwesome;
	color: #999;
	content: "\f128";
}
.faq li h6.open:before {
	content: "\f031";
}
.faq li>div {
	font-size: 16px;
	color: #999;
	display: none;
}
.faq-search {
	width: 100%;
	margin-bottom: 20px;
	border: 1px solid #e7e7e7;
	color: #ccc;
	border-radius: 5px;
	font-size: 25px;
	font-weight: 100;
	transition: .5s ease;
	padding: 12px 16px;
}
.faq-search:focus {
	color: #777;
	outline: none;
	border-color: #ccc;
}
.tabs .nav-tabs>li {
	margin-bottom: 0;
	float: none;
	display: inline-block;
}
.tabs .nav-tabs>li a {
	border: 0!important;
	border-radius: 0;
	color: #999;
	text-transform: uppercase;
	font-size: 14px;
	letter-spacing: 1px;
	font-weight: 500;
	transition: .3s ease;
}
.tabs .tab-content {
	padding: 15px;
}
.tabs-icon .nav-tabs>li a {
	padding: 10px 25px;
}
.tabs-icon .nav-tabs>li a .fa, .tabs-icon .nav-tabs>li a .glyphicon {
	display: block;
	font-size: 32px;
	margin-bottom: 15px;
	text-align: center;
}
.tabs-btn .nav-tabs {
	border-bottom: 0;
}
.tabs-btn .nav-tabs li {
	float: left;
}
.tabs-btn .nav-tabs li a {
	background-color: #eee;
	margin-right: 0;
}
code {
	color: #f4645f;
	background-color: #f0f2f1;
	text-shadow: 0 1px #fff;
}
pre {
	background-color: #fcfcfc;
	border-color: #e7e7e7;
}
.code-preview {
	padding: 16px;
}
.clipboard-copy {
	position: absolute;
	top: 26px;
	right: 13px;
	line-height: 20px;
	transition: opacity .4s ease-in-out;
	opacity: 0;
	z-index: 4;
}
.code-tabs .clipboard-copy, .code-window .clipboard-copy {
	top: 13px;
}
pre .language-name {
	background-color: transparent;
	color: #ccc;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 600;
	cursor: default;
	position: absolute;
	top: 8px;
	right: 10px;
}
:not(pre)>code[class*=language-], pre[class*=language-] {
	background-color: #fcfcfc;
	border-radius: 3px;
	padding-bottom: 8px;
	margin-top: 15px;
	margin-bottom: 25px;
	word-wrap: normal;
	border-color: #e7e7e7;
}
pre[class*=language-] {
	padding-top: 30px;
}
.line-numbers .line-numbers-rows {
	border-right-color: #ccc;
}
.code-window {
	border: 1px solid #e7e7e7;
	border-radius: 3px;
	box-shadow: 0 2px 8px rgba(0,0,0,.07);
	margin: 30px 0 20px;
}
.code-window .window-bar {
	height: 38px;
	border-top: 1px solid #eaeae9;
	border-bottom: 1px solid #dfdfde;
	background: #ebebeb;
}
.code-window .window-bar .circles {
	float: left;
	margin: 3px 10px;
}
.code-window .window-bar .circles .circle {
	display: inline-block;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background-color: #fff;
	border: 1px solid #fff;
}
.code-window .window-bar .circles .circle-red {
	background-color: #fc615c;
	border-color: #fd504a;
}
.code-window .window-bar .circles .circle-yellow {
	background-color: #fec041;
	border-color: #f0b542;
}
.code-window .window-bar .circles .circle-green {
	background-color: #33c849;
	border-color: #1bc634;
}
.code-window .window-bar .circles .window-title {
	margin-left: 16px;
	font-size: 13px;
	color: #999;
}
.code-window .window-bar .languages {
	float: right;
	margin: 3px 10px;
}
.code-window .window-bar .languages .btn-group {
	box-shadow: 0 1px 1px rgba(0,0,0,.07);
}
.code-window .window-bar .languages .btn {
	background-color: #fff;
	color: #ccc;
	text-transform: uppercase;
	font-size: 12px;
	font-weight: 500;
	transition: .5s ease;
	padding: 2px 10px;
}
.code-window .window-bar .languages .btn.active, .code-window .window-bar .languages .btn:active {
	color: #29b6f6;
	box-shadow: none;
}
.code-window :not(pre)>code[class*=language-], .code-window pre[class*=language-] {
	background-color: #fff;
	border: none;
	padding-bottom: 8px;
	margin: 0;
}
.code-window .line-numbers .line-numbers-rows {
	background-color: #f7f7f7;
	border-right: none;
	padding-bottom: 8px;
}
.code-window .line-numbers:after {
	content: '';
	background-color: #f7f7f7;
	display: inline-block;
	position: absolute;
	top: 0;
	left: 0;
	width: 39px;
	height: 32px;
}
.code-snippet {
	border: 1px solid #e7e7e7;
	border-radius: 3px;
	margin: 30px 0 20px;
}
.code-snippet>* {
	border-bottom: 1px solid #e7e7e7!important;
}
.code-snippet>:last-child {
	border-bottom: none!important;
}
.code-snippet .code-preview {
	position: relative;
	padding-top: 35px;
}
.code-snippet .code-preview:before {
	position: absolute;
	top: 8px;
	right: 12px;
	color: #ccc;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 11px;
	font-weight: 600;
	content: "Example";
}
.code-tabs {
	margin: 20px 0;
}
.code-tabs .languages {
	border-bottom: 1px solid #e7e7e7;
}
.code-tabs .languages .btn {
	background-color: #fff;
	color: #ccc;
	text-transform: uppercase;
	font-size: 14px;
	font-weight: 500;
	letter-spacing: 1px;
	transition: .5s ease;
	padding: 10px 15px;
}
.code-tabs .languages .btn.active, .code-tabs .languages .btn:active {
	color: #5cc7b2;
	box-shadow: none;
}
.code-tabs pre:before {
	display: none!important;
}
.code-tabs .window-content {
	border-bottom: 1px solid #f5f6f7;
}
.step-text {
	list-style: none;
	padding-left: 0;
	counter-reset: b;
}
.step-text li {
	position: relative;
	padding-left: 60px;
	margin-bottom: 48px;
	font-weight: 300;
}
.step-text li:before {
	content: counter(b, decimal-leading-zero) ".";
	counter-increment: b;
	position: absolute;
	left: 0;
	top: 0;
	color: #555;
	font-family: Raleway, Helvetica Neue, Helvetica, Arial, sans-serif;
	font-size: 24px;
	width: 42px;
	height: 42px;
	font-weight: 700;
}
.step-text h5 {
	font-weight: 500;
	font-size: 18px;
	padding-top: 5px;
}
.step-image {
	margin-top: 60px;
	padding-top: 32px;
	border-top: 1px solid #f5f6f7;
}
.step-image .item>img {
	display: block;
	margin: 0 auto;
}
.step-image .carousel-caption {
	position: static;
	text-align: left;
	color: #555;
	text-shadow: none;
}
.step-image .carousel-indicators {
	top: -40px;
	left: 0;
	margin-left: 0;
	text-align: left;
	width: 80%;
	counter-reset: b;
}
.step-image .carousel-indicators:before {
	content: "STEPS:";
	color: #ccc;
	font-weight: 500;
	font-size: 11px;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-right: 10px;
}
.step-image .carousel-indicators li {
	border: 0;
	text-indent: 0;
	width: 34px;
	height: 34px;
	text-align: center;
}
.step-image .carousel-indicators li:before {
	content: counter(b);
	counter-increment: b;
	font-weight: 500;
	color: #ccc;
	transition: .1s ease;
}
.step-image .carousel-indicators .active:before, .step-image .carousel-indicators li:hover:before {
	color: #555;
	font-size: 18px;
}
.step-image .carousel-control {
	text-shadow: none;
	color: #555;
	width: 34px;
	height: 34px;
	font-size: 26px;
}
.step-image .carousel-control.left, .step-image .carousel-control.right {
	background-image: none;
	top: -40px;
	bottom: auto;
	left: auto;
	right: 0;
}
.step-image .carousel-control.left {
	right: 36px;
}
.list-view li {
	border-bottom: 1px solid #f5f6f7;
	padding-bottom: 20px;
	margin-bottom: 30px;
}
.list-view li:last-child {
	border-bottom: 0;
	padding-bottom: 0;
	margin-bottom: 0;
}
.list-view h5 {
	font-weight: 300;
	margin-bottom: 0;
}
.grid-view li {
	border-top: 1px solid #5cc7b2;
	border-bottom: 1px solid #f5f6f7;
	border-right: 1px solid #f5f6f7;
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;
	margin-bottom: 3%;
	margin-right: 3%;
	width: 48%;
	display: inline-block;
	vertical-align: top;
	background-color: #fcfcfc;
	padding: 10px;
}
.grid-view li h6 {
	text-transform: uppercase;
	letter-spacing: 2px;
	font-size: 10px;
	font-weight: 700;
	color: #ccc;
	margin-bottom: 4px;
}
.grid-view h5 {
	font-weight: 300;
	font-size: 18px;
}
.categorized-view li {
	margin-bottom: 3%;
	margin-right: 3%;
	width: 48%;
	display: inline-block;
	vertical-align: top;
	padding: 0;
}
.categorized-view h5 {
	font-weight: 500;
	font-size: 15px;
	text-transform: uppercase;
	letter-spacing: 2px;
	border-bottom: 1px solid #e7e7e7;
	position: relative;
	padding: 10px 4px;
}
.categorized-view h5:before {
	content: '';
	position: absolute;
	left: 0;
	bottom: -1px;
	width: 20%;
	border-bottom: 1px solid #aaa;
}
.categorized-view a {
	position: relative;
	display: block;
	margin-bottom: 12px;
	margin-left: 24px;
	line-height: 22px;
}
.categorized-view a:before {
	position: absolute;
	left: -20px;
	top: 4px;
	content: "\f0f6";
	font: normal normal normal 14px/1 FontAwesome;
}
.color-palette-circular li {
	display: inline-block;
	width: 128px;
	height: 128px;
	line-height: 128px;
	text-align: center;
	font-weight: 600;
	border-radius: 100%;
	margin-right: 24px;
	margin-bottom: 24px;
	color: #fff;
}
.color-palette-stacked li {
	display: block;
	color: #fff;
	font-weight: 600;
	padding: 15px 20px;
}
.promo {
	text-align: center;
	margin: 24px 0;
}
.promo.left {
	text-align: left;
}
.promo.right {
	text-align: right;
}
.promo.small-icon .fa {
	float: left;
	font-size: 24px;
	display: inline-block;
	width: 26px;
	height: 26px;
	text-align: left;
	margin-right: 16px;
	margin-top: 8px;
}
.promo.small-icon>h3 {
	margin-top: 0;
	margin-bottom: 12px;
	padding-top: 6px;
	padding-bottom: 12px;
	font-size: 16px;
	border-bottom: 1px solid #f5f6f7;
	position: relative;
}
.promo.small-icon>h3:before {
	content: '';
	position: absolute;
	left: 0;
	bottom: -1px;
	width: 42px;
	border-bottom: 1px solid #e7e7e7;
}
.promo>.fa, .promo>.glyphicon {
	font-size: 90px;
	color: #5cc7b2;
}
.promo>h3 {
	font-weight: 500;
	font-size: 18px;
	margin-top: 30px;
	margin-bottom: 15px;
	letter-spacing: 2px;
	text-transform: uppercase;
}
.promo>p {
	line-height: 24px;
	color: #999;
}
.promo>img {
	display: block;
	max-width: 100%;
	height: auto;
	margin: 0 auto;
}
.promo>img.bordered {
	border-radius: 2px;
	border: 1px solid #e7e7e7;
	padding: 3px;
}
.dir-explain {
	position: relative;
	border: 1px solid #ddd;
	border-bottom: none;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	padding: 45px 15px 15px;
}
.dir-explain:before {
	position: absolute;
	top: 16px;
	left: 16px;
	font-size: 12px;
	font-weight: 500;
	letter-spacing: 1px;
	text-transform: uppercase;
	color: #ccc;
	content: 'Directory structure';
}
.dir-explain .files {
	margin-bottom: 0;
	margin-top: 5px;
	padding-left: 20px;
	list-style: none;
}
.dir-explain .files li:before {
	content: '-';
	margin-right: 10px;
}
.file-tree {
	border: 1px solid #f5f6f7;
	background-color: #fcfcfc;
	margin: 20px 0;
	padding: 20px;
}
.file-tree p {
	font-size: 14px;
	line-height: 22px;
}
.file-tree ul {
	list-style: none;
	padding-left: 26px;
	margin-bottom: 12px;
}
.file-tree>ul {
	padding-left: 0;
	margin-bottom: 0;
}
.file-tree li {
	line-height: 30px;
	position: relative;
}
.file-tree li>i {
	color: #777;
	font-size: 13px;
	padding-left: 12px;
	cursor: default;
	font-style: normal;
}
.file-tree li>i:before {
	content: "-";
	display: inline-block;
	margin-right: 4px;
}
.file-tree li.is-folder {
	cursor: pointer;
}
.file-tree li.is-file {
	font-size: 14px;
}
.file-tree li:before {
	font: normal normal normal 14px/1 FontAwesome;
	display: inline-block;
	width: 16px;
	margin-right: 6px;
}
.file-tree li.is-file:before {
	content: "\f15b";
	color: #cedde0;
}
.file-tree li.is-folder:before {
	content: "\f07b";
	cursor: pointer;
	color: #f4db0b;
}
.file-tree li.is-folder.open:before {
	content: "\f07c";
	cursor: pointer;
}
figure img {
	max-width: 100%;
	height: auto;
	display: block;
	margin: 0 auto;
}
figcaption {
	font-style: italic;
	text-align: center;
	color: #999;
}
.img-shadow {
	box-shadow: 0 0 10px 0 rgba(0,0,0,.2);
}
.banner {
	position: relative;
	height: 50vh;
	background-size: cover;
	background-position: center;
	background-attachment: fixed;
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d;
	overflow: hidden;
}
.banner.overlay-black, .banner.overlay-white {
	position: relative;
	z-index: 0;
	color: #fff;
}
.banner.overlay-black:before, .banner.overlay-white:before {
	position: absolute;
	content: ' ';
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: -1;
	background-color: rgba(0,0,0,.6);
}
.banner.overlay-white:before {
	background-color: hsla(0,0%,100%,.6);
}
.banner .container, .banner .container-fluid {
	position: relative;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}
.banner .container-fluid.text-center, .banner .container.text-center {
	max-width: 80%;
	margin: 0 auto;
}
.banner .container-fluid.text-left, .banner .container-fluid.text-right, .banner .container.text-left, .banner .container.text-right {
	max-width: 70%;
	margin: 0 30px;
}
.banner .container-fluid.text-right, .banner .container.text-right {
	margin-left: auto;
}
.banner p {
	font-size: 18px;
	line-height: 28px;
}
.banner.auto-size {
	height: auto;
	padding-top: 70px;
	padding-bottom: 48px;
}
.banner.auto-size .container, .banner.auto-size .container-fluid {
	position: static;
	-webkit-transform: translateY(0)!important;
	transform: translateY(0)!important;
	top: 0;
}
.banner.banner-sm {
	height: 35vh;
}
.banner.banner-lg {
	height: 75vh;
}
.banner.banner-full-height {
	height: 100vh;
}
.banner.banner-full-height .container, .banner.banner-full-height .container-fluid {
	-webkit-transform: translateY(-70%);
	transform: translateY(-70%);
}
.banner.banner-full-height h1 {
	font-size: 60px;
	line-height: 70px;
}
.banner .social-icons {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 32px;
	list-style: none;
	text-align: center;
	margin: 0;
	padding: 0;
}
.banner .social-icons li {
	display: inline-block;
	padding: 4px 8px;
}
.banner.has-attached-image {
	height: 100%;
}
.banner.has-attached-image .container, .banner.has-attached-image .container-fluid {
	position: static;
	-webkit-transform: translateY(0);
	transform: translateY(0);
	margin-top: 10%;
	margin-bottom: 10%;
}
.banner .attached-image img {
	max-width: 70%;
	display: block;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	box-shadow: 0 0 10px 0 rgba(0,0,0,.2);
	margin: 0 auto;
}
.carousel {
	overflow: hidden;
}
.carousel .carousel-control {
	text-shadow: none;
	background-image: none;
	width: 40%;
}
.carousel .carousel-control .fa {
	position: absolute;
	top: 50%;
	margin-top: -32px;
	color: #fff;
	font-size: 64px;
	transition: .3s ease-out;
}
.carousel .carousel-control.right .fa {
	right: -50px;
}
.carousel .carousel-control.right:hover .fa {
	right: 30px;
}
.carousel .carousel-control.left .fa {
	left: -50px;
}
.carousel .carousel-control.left:hover .fa {
	left: 30px;
}
.carousel.color-alt .carousel-indicators li {
	border-color: #777;
}
.carousel.indicators-out {
	overflow: visible;
	padding-bottom: 50px;
}
.carousel.indicators-out .carousel-indicators {
	bottom: 0;
}
.carousel.indicators-out .carousel-indicators li {
	border-color: #5cc7b2;
}
.carousel.indicators-out .carousel-indicators .active {
	background-color: #5cc7b2;
}
.jumbotron {
	background-size: cover;
	background-position: center;
	border-radius: 0!important;
	background-color: #f5f5f5;
}
.jumbotron.overlay-black, .jumbotron.overlay-white {
	position: relative;
	z-index: 0;
}
.jumbotron.overlay-black:before, .jumbotron.overlay-white:before {
	position: absolute;
	content: ' ';
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: -1;
	background-color: rgba(0,0,0,.4);
}
.jumbotron.overlay-white:before {
	background-color: hsla(0,0%,100%,.7);
}
.jumbotron p {
	font-weight: 300;
	line-height: 30px;
}
.jumbotron h1 {
	line-height: 70px;
}
.jumbotron h2 {
	border-bottom: 0;
	margin-bottom: 16px;
	line-height: 50px;
}
.jumbotron-primary {
	background-color: #2196f3;
	color: #fff;
}
.jumbotron-success {
	background-color: #4caf50;
	color: #fff;
}
.jumbotron-info {
	background-color: #29b6f6;
	color: #fff;
}
.jumbotron-warning {
	background-color: #ff9800;
	color: #fff;
}
.jumbotron-danger {
	background-color: #f44336;
	color: #fff;
}
.jumbotron-purple {
	background-color: #6d5cae;
	color: #fff;
}
.jumbotron-teal {
	background-color: #00bfa5;
	color: #fff;
}
.jumbotron-dark {
	background-color: #424242;
	color: #fff;
}
.jumbotron-white {
	background-color: #fff;
	color: #333;
}
.jumbotron.jumbotron-sm {
	padding: 48px 24px 24px;
}
.jumbotron.jumbotron-sm p {
	font-size: 19px;
	line-height: 28px;
}
.jumbotron.jumbotron-lg {
	padding: 100px 36px;
}
.jumbotron.jumbotron-lg p {
	font-size: 24px;
	line-height: 38px;
}
.jumbotron.jumbotron-xl {
	padding: 220px 50px;
}
.jumbotron.jumbotron-xl p {
	font-size: 28px;
	line-height: 42px;
}
.breadcrumb {
	border-radius: 0;
	background-color: transparent;
	border-bottom: 1px solid #e7e7e7;
	margin-bottom: 40px;
}
.pagination li>a, .pagination li>span {
	border: none;
	color: #777;
	transition: .3s ease-out;
	margin: 0 4px;
}
.pagination .next, .pagination .previous {
	font-size: 20px;
	line-height: 21px;
}
.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover {
	background-color: #5cc7b2;
	border-radius: 2px;
}
.pager li>a {
	border: none;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 14px;
	font-weight: 600;
	border-radius: 0;
	color: #777;
	transition: .3s ease-out;
}
.pager .next>a:after {
	content: "\f105";
	margin-left: 8px;
}
.pager .next>a:after, .pager .previous>a:before {
	font-family: FontAwesome;
	font-size: 20px;
	vertical-align: bottom;
}
.pager .previous>a:before {
	content: "\f104";
	margin-right: 8px;
}
.require-script {
	background-color: #fcfcfc;
	border: 1px solid #f5f6f7;
	border-radius: 3px;
	padding: 15px;
}
.require-script .css, .require-script .js {
	position: relative;
	padding-top: 25px;
}
.require-script .css:before, .require-script .js:before {
	position: absolute;
	top: 0;
	color: #ccc;
	font-weight: 500;
	font-size: 11px;
	text-transform: uppercase;
	letter-spacing: 1px;
}
.require-script .css:before {
	content: "Required Stylesheet";
}
.require-script .js:before {
	content: "Required Script";
}
.require-script>p+p {
	margin-top: 20px;
}
.lity.lity-opened, .lity-loading .lity-loader, pre:hover .clipboard-copy {
	opacity: 1;
}
.lity.lity-closed, h3 a:before {
	opacity: 0;
}
.lity-hide, .sidenav.dropable ul, .sidebar-line .sidenav>li>ul:before, .sidebar-icon .sidenav a.active:before, .sidebar-icon .sidenav li.active a:before, .sidebar-icon .sidenav>li>ul:before, .sidebar-boxed .sidenav a.active:before, .sidebar-boxed .sidenav>li>ul:before, pre[class*=language-][data-language]:before, .code-window pre[class*=language-][data-language]:before, .file-tree ul ul {
	display: none;
}
body[data-spy], .site-footer .container, .site-footer .container-fluid, .sidenav>li>ul a.active, .sidenav>li>ul li.active a, .sidebar-line .sidenav a.active, .sidebar-line .sidenav li.active>a {
	position: relative;
}
.main-content>h1, .main-content>header h1, .jumbotron.jumbotron-sm h1, .jumbotron.jumbotron-sm h2, .jumbotron.jumbotron-sm h3, .jumbotron.jumbotron-sm h4, .jumbotron.jumbotron-sm h5 {
	margin-top: 0;
}
.container-fluid .main-content, .container-fluid .sidebar {
	padding-left: 30px;
}
.navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover, .sidebar-boxed.sidebar-dark .sidenav a.active, .sidebar-boxed.sidebar-dark .sidenav a:hover, .sidebar-boxed.sidebar-dark .sidenav li.active>a, .text-white, .btn-dark, .btn-dark.active, .btn-dark.focus, .btn-dark:active, .btn-dark:focus, .btn-dark:hover, .btn-purple, .btn-purple.active, .btn-purple.focus, .btn-purple:active, .btn-purple:focus, .btn-purple:hover, .btn-teal, .btn-teal.active, .btn-teal.focus, .btn-teal:active, .btn-teal:focus, .btn-teal:hover, .banner .social-icons a {
	color: #ffffff;
	outline: none;
	background: #7f00ff;
}
.navbar-default .navbar-toggle .fa, .site-footer #scroll-up>.fa {
	vertical-align: top;
}
.navbar-default.navbar-light li>a, .sidenav a:focus, .list-view p, .grid-view p, .carousel.color-alt .carousel-control .fa {
	color: #777;
}
.navbar-default.navbar-light .active>a, .navbar-default.navbar-light .active>a:focus, .navbar-default.navbar-light .active>a:hover, .navbar-default.navbar-light li>a:focus, .navbar-default.navbar-light li>a:hover, .navbar-default.navbar-light .open>a:focus, .navbar-default.navbar-light .open>a:hover, .sidenav>li>a.active, .sidenav>li>a:hover, .sidenav li.active>a, .sidebar-boxed .sidenav>li>a.open, .sidebar-boxed .sidenav>li>a:hover, a, a:focus, a:hover, .tabs-text .nav-tabs>li.active a, .tabs-icon .nav-tabs>li.active a, .tabs-btn .nav-tabs li.active a {
	color: #7f00ff;
}
.navbar-default.navbar-light .dropdown-menu, .code-tabs .code-preview {
	background-color: #fcfcfc;
}
.navbar-default.navbar-light .dropdown-menu a:hover, .banner.overlay-white {
	color: #333;
}
.sidenav.nav>li>a, .sidenav>li>ul.nav>li>a {
	padding: 0;
}
.sidenav.nav>li>a:focus, .sidenav.nav>li>a:hover, .sidenav>li>ul.nav>li>a:focus, .sidenav>li>ul.nav>li>a:hover, .tabs .nav-tabs>li a:hover {
	background-color: transparent;
}
.sidenav.dropable .active>ul, .sidenav.dropable a.active+ul, .sidenav.dropable ul.open, .file-tree li.is-folder.open>ul {
	display: block;
}
.sidenav a.active, p, ul, h4, h5, .file-tree h5 {
	font-weight: 400;
}
.sidebar-boxed .sidenav a.open, .sidebar-boxed .sidenav a.open+ul, .sidebar-boxed .sidenav>li>a:hover {
	background-color: #f8b604;
}
.sidebar-boxed.sidebar-dark .sidenav>li>a.open, .sidebar-boxed.sidebar-dark .sidenav>li>a:hover, .btn-outline.active, .btn-outline.focus, .btn-outline:active, .btn-outline:focus, .btn-outline:hover, .btn-round.active, .btn-round.focus, .btn-round:active, .btn-round:focus, .btn-round:hover {
	color: #ffffff !important;
}
a:focus, .clipboard-copy:hover {
	text-decoration: none;
}
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, ::-webkit-input-placeholder, .table-info, .banner h1 strong, .banner p {
	font-weight:700;
}
h1 small, .banner h1 {
	font-weight: 100;
	color:#fff
}

::-moz-placeholder, :-ms-input-placeholder {
color:red;
}
.text-gray, .line-numbers-rows>span:before, .code-window .line-numbers-rows>span:before {
	color: #ccc;
}
.label-primary, .btn-primary {
	background-color: #2196f3;
}
.label-success, .btn-success, .change-added {
	background-color: #4caf50;
}
.label-info, .btn-info, .change-updated {
	background-color: #29b6f6;
}
.label-warning, .btn-warning, .change-fixed {
	background-color: #ff9800;
}
.label-danger, .btn-danger, .change-removed {
	background-color: #f44336;
}
.label-purple, .btn-purple, .change-moved {
	background-color: #6d5cae;
}
.label-teal, .btn-teal, .change-merged {
	background-color: #00bfa5;
}
.label-dark, .btn-dark {
	background-color: #424242;
}
.btn-outline.btn-default, .btn-round.btn-default {
	color: #777;
	border-color: #777;
}
.btn-outline.btn-default.active, .btn-outline.btn-default.focus, .btn-outline.btn-default:active, .btn-outline.btn-default:focus, .btn-outline.btn-default:hover, .btn-round.btn-default.active, .btn-round.btn-default.focus, .btn-round.btn-default:active, .btn-round.btn-default:focus, .btn-round.btn-default:hover, .carousel.color-alt .carousel-indicators .active {
	background-color: #777;
}
.btn-outline.btn-primary, .btn-round.btn-primary {
	color: #2196f3;
	border-color: #2196f3;
}
.btn-outline.btn-success, .btn-round.btn-success {
	color: #4caf50;
	border-color: #4caf50;
}
.btn-outline.btn-info, .btn-round.btn-info {
	color: #29b6f6;
	border-color: #29b6f6;
}
.btn-outline.btn-warning, .btn-round.btn-warning {
	color: #ff9800;
	border-color: #ff9800;
}
.btn-outline.btn-danger, .btn-round.btn-danger {
	color: #f44336;
	border-color: #f44336;
}
.btn-outline.btn-purple, .btn-round.btn-purple {
	color: #6d5cae;
	border-color: #6d5cae;
}
.btn-outline.btn-teal, .btn-round.btn-teal {
	color: #00bfa5;
	border-color: #00bfa5;
}
.btn-outline.btn-dark, .btn-round.btn-dark {
	color: #424242;
	border-color: #424242;
}
.btn-outline.btn-white, .btn-round.btn-white {
	color: #fff;
	border-color: #fff;
}
.tabs-text .nav-tabs>li.active, .tabs-icon .nav-tabs>li.active {
	margin-bottom: -1px;
	border-bottom: 1px solid #5cc7b2;
}
.code-snippet :not(pre)>code[class*=language-], .code-snippet pre[class*=language-], .code-tabs pre {
	border: 0;
	border-radius: 0;
	margin: 0;
}
.list-view, .grid-view, .categorized-view {
	list-style: none;
	margin: 0;
	padding: 0;
}
.list-view h5 a, .grid-view h5 a {
	color: #45baa3;
}
.list-view .meta-data, .grid-view .meta-data {
	font-size: 12px;
	font-style: italic;
	color: #999;
}
.grid-view li:nth-child(2n), .grid-view.view-col-3 li:nth-child(3n), .categorized-view li:nth-child(2n), .categorized-view.view-col-3 li:nth-child(3n) {
	margin-right: 0;
}
.grid-view.view-col-3 li, .categorized-view.view-col-3 li {
	margin-right: 2.5%;
	width: 31%;
}
.grid-view.view-col-3 li:nth-child(2n), .categorized-view.view-col-3 li:nth-child(2n) {
	margin-right: 2.5%;
}
.color-palette-circular, .color-palette-stacked {
	list-style: none;
	padding-left: 0;
	margin: 24px 0;
}
.promo>.btn, .jumbotron .btn {
	margin-top: 15px;
}
.banner.banner-sm h1, .banner.banner-lg h1 {
	font-size: 40px;
	line-height: 45px;
}
.pagination>li>a:focus, .pagination>li>a:hover, .pagination>li>span:focus, .pagination>li>span:hover, .pager li>a:hover {
	background-color: transparent;
	color: #5cc7b2;
}
 @media screen and max-width991px {
.main-content {
padding:50px 30px 50px 0;
}
 .sidebar+.main-content {
padding-left:30px;
}
 .site-header .navbar {
padding:15px;
}
 .navbar-brand {
float:left;
}
 .navbar-brand>img {
max-height:32px;
}
 .sidebar-boxed {
width:240px;
}
 .sidebar-boxed~footer, .sidebar-boxed~header, .sidebar-boxed~main {
padding-left:240px;
}
 .sidebar-boxed~header.navbar-transparent .navbar {
right:0;
left:240px;
}
 .sidebar-boxed~footer .row, .sidebar-boxed~header .banner {
padding-left:15px;
padding-right:15px;
}
 .sidebar-boxed~main .main-content {
padding-left:30px;
padding-right:30px;
}
 .sidebar-boxed.sidebar-right~footer, .sidebar-boxed.sidebar-right~header, .sidebar-boxed.sidebar-right~main {
padding-left:0;
padding-right:240px;
}
 .sidebar-boxed.sidebar-right~header.navbar-transparent .navbar {
right:240px;
left:0;
}
 .sidebar-boxed .sidenav ul a {
padding-left:38px;
}
 h1 {
font-size:45px;
margin-bottom:30px;
}
 h2 {
font-size:32px;
margin-bottom:28px;
margin-top:55px;
}
 h3 {
font-size:28px;
margin-bottom:25px;
margin-top:38px;
}
 h4 {
font-size:24px;
line-height:28px;
}
 h5 {
font-size:20px;
line-height:25px;
}
 .clipboard-copy {
top:8px!important;
right:8px;
color:#ccc;
background-color:transparent;
text-transform:uppercase;
letter-spacing:1px;
font-weight:600;
opacity:1;
padding:0;
}
 .clipboard-copy:focus, .clipboard-copy:hover {
color:#555;
background-color:transparent;
}
 pre .language-name, pre[class*=language-][data-language]:before {
right:56px;
}
 .categorized-view.view-col-3 li {
margin-right:3%;
width:48%;
}
 .categorized-view.view-col-3 li:nth-child(2n) {
margin-right:0;
}
 .categorized-view.view-col-3 li:nth-child(3n) {
margin-right:3%;
}
 .promo {
margin-bottom:20px;
padding-bottom:20px;
border-bottom:1px solid #f5f6f7;
}
 .promo h3 {
margin-bottom:15px;
}
 .row>div:last-child>.promo {
border-bottom:0;
padding-bottom:0;
margin-bottom:0;
}
 .banner .attached-image img {
border-top-left-radius:8px;
border-top-right-radius:8px;
}
 .carousel .carousel-control.right .fa {
right:30px;
}
 .carousel .carousel-control.left .fa {
left:30px;
}
 .jumbotron h1 {
line-height:40px;
}
 .jumbotron h2 {
line-height:35px;
}
 .jumbotron.jumbotron-lg {
padding:90px 24px;
}
 .jumbotron.jumbotron-xl {
padding:180px 32px;
}
 .jumbotron.jumbotron-lg p, .jumbotron.jumbotron-xl p {
font-size:18px;
line-height:26px;
}
}
 @media screen and max-width767px {
.main-content {
padding:30px 20px!important;
}
 .container-fluid .sidebar+.main-content {
padding-right:20px;
}
 .container-fluid .main-content.col-md-12 {
padding-left:20px;
padding-right:20px;
}
 .site-header>.navbar {
padding:2px 15px 0!important;
}
 .navbar-default .navbar-brand {
font-size:22px;
}
 .navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover {
color:hsla(0,0%,100%,.9);
background-color:transparent;
}
 .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
color:#fff;
font-weight:500;
}
 .navbar-default .navbar-nav .open .dropdown-menu>li>a {
color:hsla(0,0%,100%,.9);
}
 .navbar-default .navbar-toggle {
margin-top:3px;
}
 .site-header .navbar-brand {
display:block;
text-align:center;
}
 .site-header .navbar-brand>img {
max-height:20px!important;
}
 .site-header.navbar-transparent .navbar-nav {
background-color:#292929;
}
 .navbar {
position:fixed!important;
top:0!important;
left:0!important;
right:0!important;
z-index:7!important;
box-shadow:0 0 10px 0 rgba(0,0,0,.2)!important;
background-color:#5cc7b2!important;
}
 .footer-menu, .site-footer {
text-align:center;
}
 .footer-menu {
margin-top:20px;
}
 body, html {
overflow-x:hidden;
}
 .site-header, .site-header .navbar {
transition:left .25s ease-out;
}
 .site-header {
position:relative;
left:0;
}
 .site-footer {
padding-left:15px;
padding-right:15px;
}
 body>main {
position:relative;
left:0;
padding-left:15px!important;
padding-right:15px!important;
transition:left .25s ease-out;
}
 .sidebar-boxed~footer, .sidebar-boxed~header, .sidebar-boxed~main {
padding-left:0;
}
 .sidebar-boxed.sidebar-right~footer, .sidebar-boxed.sidebar-right~header, .sidebar-boxed.sidebar-right~main {
padding-left:0;
padding-right:0;
}
 .sidebar {
position:fixed;
top:0;
bottom:0;
width:80%!important;
height:100%;
left:-80%!important;
padding-left:25px;
padding-top:20px!important;
overflow-y:auto;
background-color:#fcfcfc!important;
transition:left .25s ease-out;
}
 .sidebar.sidebar-boxed .sidenav a {
padding-left:0!important;
padding-top:0!important;
padding-bottom:0!important;
}
 .sidebar.sidebar-boxed .sidenav ul a {
padding-left:18px!important;
}
 .sidenav.dropable ul {
display:block;
}
 .open-sidebar {
overflow:hidden;
}
 .open-sidebar .site-header .navbar {
left:80%!important;
right:-80%!important;
}
 .open-sidebar .sidebar {
left:0!important;
}
 .open-sidebar .site-header.navbar-transparent .navbar {
position:fixed;
}
 section>h2[id]>a {
margin-left:30px;
}
 .tabs .nav-tabs>li a {
font-size:12px;
}
 .banner .container, .banner .container-fluid {
-webkit-transform:translateY(-50%)!important;
transform:translateY(-50%)!important;
max-width:100%!important;
}
 .banner h1 {
font-size:30px!important;
line-height:38px!important;
}
 .banner p {
font-size:16px;
line-height:22px;
}
 .banner .btn {
margin-bottom:8px;
}
 .banner.has-attached-image {
height:100%!important;
}
 .banner.has-attached-image .container, .banner.has-attached-image .container-fluid {
position:static;
-webkit-transform:translateY(0)!important;
transform:translateY(0)!important;
margin-top:20%;
margin-bottom:15%;
}
 .banner .attached-image img {
max-width:90%;
border-top-left-radius:4px;
border-top-right-radius:4px;
}
 .jumbotron.jumbotron-lg {
padding:70px 16px;
}
 .jumbotron.jumbotron-xl {
padding:100px 20px;
}
 .container-fluid .main-content, .container-fluid .sidebar {
padding-left:20px;
}
 .sidebar-boxed~header.navbar-transparent .navbar, .sidebar-boxed.sidebar-right~header.navbar-transparent .navbar {
right:0;
left:0;
}
 .sidebar.sidebar-boxed .has-child:after, .sidebar .sidebar-brand, .open-sidebar .site-footer, .sidenav>li.active a:before {
display:none;
}
 .sidebar.sidebar-boxed .sidenav a:hover, .sidebar.sidebar-boxed .sidenav a.open, .sidebar.sidebar-boxed .sidenav a.open+ul {
background-color:transparent;
}
 .open-sidebar .site-header, .open-sidebar>main {
left:80%;
}
 .sidenav>li, .sidenav>li>ul a.active {
position:static;
}
}
 @media screen and min-width1170px {
.sidebar-boxed~footer .row, .sidebar-boxed~header .banner {
padding-left:120px;
padding-right:120px;
}
 .sidebar-boxed~main .main-content {
padding-left:135px;
padding-right:135px;
}
}
 @media screen and max-width460px {
h1 {
font-size:32px;
margin-bottom:25px;
}
 h2 {
font-size:28px;
margin-top:40px;
}
 h3 {
font-size:25px;
margin-bottom:23px;
margin-top:30px;
}
 h4 {
font-size:22px;
line-height:26px;
font-weight:300;
}
 h5 {
font-size:19px;
line-height:24px;
font-weight:400;
}
 h6 {
font-size:17px;
line-height:20px;
font-weight:500;
}
}
 @media screen and max-width480px {
.callout {
padding:10px;
}
 .code-window .window-bar .languages .btn {
padding:2px 6px;
}
 .categorized-view li, .grid-view li {
width:100%!important;
margin-right:0!important;
}
 .categorized-view li {
padding:0;
}
 .file-tree li>i {
display:block;
margin:-5px 0 6px 12px;
}
 .require-script .css, .require-script .js {
word-break:break-all;
}
}