import * as React from "react";
import type { SVGProps } from "react";
const Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 42 42" {...props}><defs><linearGradient xmlns="http://www.w3.org/2000/svg" id="prefix__a" x1={189.969} x2={189.969} y1={-141.667} y2={1567.079} gradientUnits="userSpaceOnUse"><stop stopColor="#fff" stopOpacity={0.6} /><stop offset={0.75} stopColor="#fff" stopOpacity={0.15} /><stop offset={1} stopColor="#fff" stopOpacity={0} /></linearGradient></defs><g xmlns="http://www.w3.org/2000/svg"><path fill="url(#prefix__a)" d="M198.52-124.58a8.54 8.54 0 0 0 8.54-8.543 8.54 8.54 0 0 0-8.54-8.544c-4.72 0-8.55 3.825-8.55 8.544 0 4.718 3.83 8.543 8.55 8.543" /><path stroke="#C4C4C5" strokeLinecap="round" strokeWidth={2.625} d="M205.1-121.557a13.15 13.15 0 0 1-9.15 1.511 13.14 13.14 0 0 1-7.88-4.9 13.2 13.2 0 0 1-2.7-8.88 13.23 13.23 0 0 1 3.83-8.452M209.93-139.55a13.16 13.16 0 0 1 1.65 8.305 13.2 13.2 0 0 1-3.75 7.595M198.52-146.136a13.17 13.17 0 0 1 9.31 3.858M191.93-144.37c1-.577 2.07-1.019 3.18-1.317" /></g></svg>;
export default Component;