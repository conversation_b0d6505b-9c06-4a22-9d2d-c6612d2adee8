Configuration,Rule Source,Sarif Identifier,Alerts
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/angular/disabling-sce,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/angular/double-compilation,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/angular/insecure-url-whitelist,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/bad-code-sanitization,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/bad-tag-filter,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/biased-cryptographic-random,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/build-artifact-leak,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/case-sensitive-middleware-path,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/clear-text-cookie,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/clear-text-logging,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/clear-text-storage-of-sensitive-data,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/client-exposed-cookie,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/client-side-unvalidated-url-redirection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/code-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/command-line-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/cors-misconfiguration-for-credentials,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/cross-window-information-leak,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/disabling-certificate-validation,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/disabling-electron-websecurity,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/double-escaping,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/enabling-electron-insecure-content,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/exposure-of-private-files,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/functionality-from-untrusted-domain,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/functionality-from-untrusted-source,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/host-header-forgery-in-email-generation,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/html-constructed-from-input,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/identity-replacement,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-hostname-regexp,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-html-attribute-sanitization,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-multi-character-sanitization,8
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-sanitization,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-url-scheme-check,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incomplete-url-substring-sanitization,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/incorrect-suffix-check,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insecure-dependency,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insecure-download,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insecure-helmet-configuration,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insecure-randomness,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insufficient-key-size,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/insufficient-password-hash,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/jwt-missing-verification,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/loop-bound-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/missing-rate-limiting,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/missing-token-validation,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/overly-large-range,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/path-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/polynomial-redos,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/prototype-polluting-assignment,2
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/prototype-pollution,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/prototype-pollution-utility,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/redos,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/reflected-xss,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/regex-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/request-forgery,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/resource-exhaustion,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/resource-exhaustion-from-deep-object-traversal,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/second-order-command-line-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/sensitive-get-query,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/server-crash,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/server-side-unvalidated-url-redirection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/shell-command-constructed-from-input,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/shell-command-injection-from-environment,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/sql-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/stack-trace-exposure,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/stored-xss,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/summary/lines-of-code,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/summary/lines-of-user-code,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/tainted-format-string,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/template-object-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/type-confusion-through-parameter-tampering,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unnecessary-use-of-cat,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unsafe-deserialization,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unsafe-dynamic-method-access,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unsafe-html-expansion,2
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unsafe-jquery-plugin,2
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/unvalidated-dynamic-method-call,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/useless-regexp-character-escape,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/weak-cryptographic-algorithm,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xml-bomb,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xpath-injection,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xss,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xss-through-dom,13
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xss-through-exception,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/xxe,0
/language:javascript-typescript,codeql/javascript-queries (2.0.1+da3e5479df71bcec4a0b8e385187065dc6a63eeb),js/zipslip,0
