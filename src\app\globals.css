@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fonts are loaded via Next.js font optimization in layout.tsx */

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  font-family: 'Inter', system-ui, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Thin scrollbar for gallery */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.2);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.4);
}

/* Hide scrollbar completely */
.scrollbar-none {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-gradient {
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  .animate-in {
    animation: fadeInUp 0.3s ease-out forwards;
  }

  .fade-in-0 {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .slide-in-from-bottom-2 {
    animation: slideInFromBottom 0.3s ease-out forwards;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white;
  }
}

@layer base {
  :root {
    --background: oklch(0.9940 0 0);
    --foreground: oklch(0 0 0);
    --card: oklch(0.9940 0 0);
    --card-foreground: oklch(0 0 0);
    --popover: oklch(0.9911 0 0);
    --popover-foreground: oklch(0 0 0);
    --primary: oklch(0.5393 0.2713 286.7462);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9540 0.0063 255.4755);
    --secondary-foreground: oklch(0.1344 0 0);
    --muted: oklch(0.9702 0 0);
    --muted-foreground: oklch(0.4386 0 0);
    --accent: oklch(0.9393 0.0288 266.3680);
    --accent-foreground: oklch(0.5445 0.1903 259.4848);
    --destructive: oklch(0.6290 0.1902 23.0704);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9300 0.0094 286.2156);
    --input: oklch(0.9401 0 0);
    --ring: oklch(0 0 0);
    --chart-1: oklch(0.7459 0.1483 156.4499);
    --chart-2: oklch(0.5393 0.2713 286.7462);
    --chart-3: oklch(0.7336 0.1758 50.5517);
    --chart-4: oklch(0.5828 0.1809 259.7276);
    --chart-5: oklch(0.5590 0 0);
    --radius: 1.4rem;
    --color-1: oklch(66.2% 0.225 25.9);
    --color-2: oklch(60.4% 0.26 302);
    --color-3: oklch(69.6% 0.165 251);
    --color-4: oklch(80.2% 0.134 225);
    --color-5: oklch(90.7% 0.231 133);
    --sidebar: oklch(0.9777 0.0051 247.8763);
    --sidebar-foreground: oklch(0 0 0);
    --sidebar-primary: oklch(0 0 0);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.9401 0 0);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0.9401 0 0);
    --sidebar-ring: oklch(0 0 0);
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.16;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: -0.025em;
    --spacing: 0.27rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-sm: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow-md: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 2px 4px -1px hsl(0 0% 0% / 0.16);
    --shadow-lg: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 4px 6px -1px hsl(0 0% 0% / 0.16);
    --shadow-xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 8px 10px -1px hsl(0 0% 0% / 0.16);
    --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.40);
    --tracking-normal: -0.025em;
    
    /* Dashboard-specific gradient definitions */
    --dashboard-gradient-blue: linear-gradient(0deg, rgba(208,227,241,1) 0%, rgba(41,73,98,1) 100%);
    --dashboard-gradient-orange: #ffa700;
    --dashboard-gradient-cyan: linear-gradient(0deg, rgba(229,229,229,1) 0%, rgba(0,210,255,1) 35%, rgba(0,157,241,1) 100%);
    --dashboard-gradient-dark: linear-gradient(0deg, rgba(136,136,136,1) 0%, rgba(0,0,0,1) 100%);
    --dashboard-gradient-purple: linear-gradient(0deg, rgba(174,145,202,1) 0%, rgba(89,42,133,1) 60%, rgba(24,7,40,1) 100%);
    --dashboard-gradient-green: linear-gradient(0deg, rgba(187,202,145,1) 0%, rgba(60,135,143,1) 60%, rgba(7,40,15,1) 100%);
    --dashboard-dark-bg: #1b1c1d;
    --dashboard-overlay-dark: rgba(0, 0, 0, 0.70);
    --dashboard-overlay-gradient: linear-gradient(0deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 50%);
    --dashboard-blur-bg: rgba(245, 245, 245, 0.75);
    --dashboard-border-light: #e5e5e5;
    --dashboard-shadow-card: 0px 8px 10px rgba(0, 0, 0, 0.10), 0px 20px 25px rgba(0, 0, 0, 0.10);
    --dashboard-shadow-float: 0px 0px 0px 10px #f1f1f1;
    --dashboard-shadow-inset: inset 0px 1px 5px rgba(255, 255, 255, 0.10), inset 0px 1px 1px rgba(255, 255, 255, 0.10), inset 0px 0px 0px 0.5px rgba(0, 0, 0, 0.10);
  }
  .dark {
    --background: oklch(0.2223 0.0060 271.1393);
    --foreground: oklch(0.9551 0 0);
    --card: oklch(0.2568 0.0076 274.6528);
    --card-foreground: oklch(0.9551 0 0);
    --popover: oklch(0.2568 0.0076 274.6528);
    --popover-foreground: oklch(0.9551 0 0);
    --primary: oklch(0.6132 0.2294 291.7437);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.2940 0.0130 272.9312);
    --secondary-foreground: oklch(0.9551 0 0);
    --muted: oklch(0.2940 0.0130 272.9312);
    --muted-foreground: oklch(0.7058 0 0);
    --accent: oklch(0.2795 0.0368 260.0310);
    --accent-foreground: oklch(0.7857 0.1153 246.6596);
    --destructive: oklch(0.7106 0.1661 22.2162);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3289 0.0092 268.3843);
    --input: oklch(0.3289 0.0092 268.3843);
    --ring: oklch(0.6132 0.2294 291.7437);
    --chart-1: oklch(0.8003 0.1821 151.7110);
    --chart-2: oklch(0.6132 0.2294 291.7437);
    --chart-3: oklch(0.8077 0.1035 19.5706);
    --chart-4: oklch(0.6691 0.1569 260.1063);
    --chart-5: oklch(0.7058 0 0);
    --color-1: oklch(66.2% 0.225 25.9);
    --color-2: oklch(60.4% 0.26 302);
    --color-3: oklch(69.6% 0.165 251);
    --color-4: oklch(80.2% 0.134 225);
    --color-5: oklch(90.7% 0.231 133);
    --radius: 1.4rem;
    --sidebar: oklch(0.2011 0.0039 286.0396);
    --sidebar-foreground: oklch(0.9551 0 0);
    --sidebar-primary: oklch(0.6132 0.2294 291.7437);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.2940 0.0130 272.9312);
    --sidebar-accent-foreground: oklch(0.6132 0.2294 291.7437);
    --sidebar-border: oklch(0.3289 0.0092 268.3843);
    --sidebar-ring: oklch(0.6132 0.2294 291.7437);
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.16;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: -0.025em;
    --spacing: 0.27rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-sm: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow-md: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 2px 4px -1px hsl(0 0% 0% / 0.16);
    --shadow-lg: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 4px 6px -1px hsl(0 0% 0% / 0.16);
    --shadow-xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.16), 0px 8px 10px -1px hsl(0 0% 0% / 0.16);
    --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.40);
  }
  .theme {
    --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-mono: IBM Plex Mono, monospace;
    --font-serif: Lora, serif;
    --radius: 1.4rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme inline {
  @keyframes rainbow {
  0% {
    background-position: 0%;
    }
  100% {
    background-position: 200%;
    }
  }
}