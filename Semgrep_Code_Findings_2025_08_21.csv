Id,Rule Name,Product,Severity,Status,Confidence,Category,Is Pro Rule,Assistant <PERSON>age Result,Assistant Triage Reason,Assistant Component,Repository Name,Repository Url,Line Of Code Url,Semgrep Platform Link,Created At,Last Opened At,Branch,Triaged At,Triage Comment,Triage Reason,Rule Description
237014221,javascript.node-crypto.security.create-de-cipher-no-iv.create-de-cipher-no-iv,Code,High,Open,High,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/security.ts#L90,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014221,2025-08-21 03:25:36.786714,2025-08-21 03:25:36.708695,refs/heads/master,,,,"The deprecated functions 'createCipher' and 'createDecipher' generate the same initialization vector every time. For counter modes such as CTR, GCM, or CCM this leads to break of both confidentiality and integrity, if the key is used more than once. Other modes are still affected in their strength, though they're not completely broken. Use 'createCipheriv' or 'createDecipheriv' instead."
237014220,javascript.node-crypto.security.create-de-cipher-no-iv.create-de-cipher-no-iv,Code,High,Open,High,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/security.ts#L106,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014220,2025-08-21 03:25:36.786697,2025-08-21 03:25:36.708691,refs/heads/master,,,,"The deprecated functions 'createCipher' and 'createDecipher' generate the same initialization vector every time. For counter modes such as CTR, GCM, or CCM this leads to break of both confidentiality and integrity, if the key is used more than once. Other modes are still affected in their strength, though they're not completely broken. Use 'createCipheriv' or 'createDecipheriv' instead."
237014219,javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal,Code,Medium,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/database/migrations.ts#L162,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014219,2025-08-21 03:25:36.786681,2025-08-21 03:25:36.708686,refs/heads/master,,,,"Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first."
237014218,javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop,Code,Medium,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/profile/ProfileForm.tsx#L182,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014218,2025-08-21 03:25:36.786665,2025-08-21 03:25:36.708682,refs/heads/master,,,,"Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object."
237014217,problem-based-packs.insecure-transport.js-node.http-request.http-request,Code,Medium,Open,Medium,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/fix-generation.js#L9,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014217,2025-08-21 03:25:36.786649,2025-08-21 03:25:36.708678,refs/heads/master,,,,"Checks for requests sent to http:// URLs. This is dangerous as the server is attempting to connect to a website that does not encrypt traffic with TLS. Instead, only send requests to https:// URLs."
237014216,problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server,Code,Medium,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/fix-generation.js#L20,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014216,2025-08-21 03:25:36.786633,2025-08-21 03:25:36.708673,refs/heads/master,,,,"Checks for any usage of http servers instead of https servers. Encourages the usage of https protocol instead of http, which does not have TLS and is therefore unencrypted. Using http can lead to man-in-the-middle attacks in which the attacker is able to read sensitive information."
237014215,typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml,Code,Medium,Open,Medium,security,True,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/client-security.tsx#L128,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014215,2025-08-21 03:25:36.786616,2025-08-21 03:25:36.708668,refs/heads/master,,,,"Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML."
237014214,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L75,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014214,2025-08-21 03:25:36.786600,2025-08-21 03:25:36.708663,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014213,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L79,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014213,2025-08-21 03:25:36.786584,2025-08-21 03:25:36.708659,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014212,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L106,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014212,2025-08-21 03:25:36.786567,2025-08-21 03:25:36.708654,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014211,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L113,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014211,2025-08-21 03:25:36.786551,2025-08-21 03:25:36.708649,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014210,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L129,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014210,2025-08-21 03:25:36.786534,2025-08-21 03:25:36.708645,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014209,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L154,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014209,2025-08-21 03:25:36.786518,2025-08-21 03:25:36.708640,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014208,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L162,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014208,2025-08-21 03:25:36.786502,2025-08-21 03:25:36.708635,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014207,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/manual-polling-test.js#L251,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014207,2025-08-21 03:25:36.786486,2025-08-21 03:25:36.708630,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014206,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/scripts/add-minimax-models.js#L175,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014206,2025-08-21 03:25:36.786470,2025-08-21 03:25:36.708623,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014205,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/scripts/add-minimax-models.js#L178,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014205,2025-08-21 03:25:36.786453,2025-08-21 03:25:36.708619,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014204,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/scripts/migrate-video-urls.js#L132,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014204,2025-08-21 03:25:36.786437,2025-08-21 03:25:36.708614,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014203,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-generation/route.ts#L35,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014203,2025-08-21 03:25:36.786421,2025-08-21 03:25:36.708610,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014202,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-generation/route.ts#L72,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014202,2025-08-21 03:25:36.786404,2025-08-21 03:25:36.708605,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014201,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-generation/route.ts#L89,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014201,2025-08-21 03:25:36.786388,2025-08-21 03:25:36.708600,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014200,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-schema/route.ts#L55,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014200,2025-08-21 03:25:36.786372,2025-08-21 03:25:36.708595,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014199,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-schema/route.ts#L71,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014199,2025-08-21 03:25:36.786356,2025-08-21 03:25:36.708591,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014198,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/admin/fix-schema/route.ts#L88,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014198,2025-08-21 03:25:36.786340,2025-08-21 03:25:36.708586,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014197,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L59,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014197,2025-08-21 03:25:36.786324,2025-08-21 03:25:36.708581,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014196,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L68,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014196,2025-08-21 03:25:36.786307,2025-08-21 03:25:36.708575,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014195,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L212,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014195,2025-08-21 03:25:36.786291,2025-08-21 03:25:36.708570,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014194,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L220,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014194,2025-08-21 03:25:36.786275,2025-08-21 03:25:36.708565,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014193,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L237,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014193,2025-08-21 03:25:36.786259,2025-08-21 03:25:36.708560,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014192,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L283,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014192,2025-08-21 03:25:36.786242,2025-08-21 03:25:36.708555,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014191,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L288,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014191,2025-08-21 03:25:36.786226,2025-08-21 03:25:36.708550,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014190,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L332,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014190,2025-08-21 03:25:36.786210,2025-08-21 03:25:36.708545,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014189,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L342,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014189,2025-08-21 03:25:36.786194,2025-08-21 03:25:36.708540,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014188,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/enhance-prompt/route.ts#L343,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014188,2025-08-21 03:25:36.786178,2025-08-21 03:25:36.708535,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014187,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L86,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014187,2025-08-21 03:25:36.786161,2025-08-21 03:25:36.708530,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014186,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L97,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014186,2025-08-21 03:25:36.786145,2025-08-21 03:25:36.708525,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014185,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L125,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014185,2025-08-21 03:25:36.786129,2025-08-21 03:25:36.708520,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014184,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L212,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014184,2025-08-21 03:25:36.786112,2025-08-21 03:25:36.708515,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014183,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L246,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014183,2025-08-21 03:25:36.786096,2025-08-21 03:25:36.708510,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014182,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L312,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014182,2025-08-21 03:25:36.786080,2025-08-21 03:25:36.708505,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014181,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L321,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014181,2025-08-21 03:25:36.786064,2025-08-21 03:25:36.708500,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014180,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L328,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014180,2025-08-21 03:25:36.786048,2025-08-21 03:25:36.708495,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014179,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L336,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014179,2025-08-21 03:25:36.786032,2025-08-21 03:25:36.708490,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014178,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L380,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014178,2025-08-21 03:25:36.786016,2025-08-21 03:25:36.708485,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014177,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L396,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014177,2025-08-21 03:25:36.786000,2025-08-21 03:25:36.708480,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014176,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L419,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014176,2025-08-21 03:25:36.785983,2025-08-21 03:25:36.708475,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014175,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L430,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014175,2025-08-21 03:25:36.785967,2025-08-21 03:25:36.708470,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014174,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L452,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014174,2025-08-21 03:25:36.785951,2025-08-21 03:25:36.708464,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014173,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L467,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014173,2025-08-21 03:25:36.785935,2025-08-21 03:25:36.708459,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014172,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L509,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014172,2025-08-21 03:25:36.785919,2025-08-21 03:25:36.708454,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014171,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L525,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014171,2025-08-21 03:25:36.785903,2025-08-21 03:25:36.708448,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014170,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L536,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014170,2025-08-21 03:25:36.785887,2025-08-21 03:25:36.708439,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014169,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L556,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014169,2025-08-21 03:25:36.785871,2025-08-21 03:25:36.708434,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014168,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/image/route.ts#L557,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014168,2025-08-21 03:25:36.785854,2025-08-21 03:25:36.708429,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014167,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L134,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014167,2025-08-21 03:25:36.785838,2025-08-21 03:25:36.708424,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014166,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L140,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014166,2025-08-21 03:25:36.785822,2025-08-21 03:25:36.708418,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014165,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L145,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014165,2025-08-21 03:25:36.785806,2025-08-21 03:25:36.708412,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014164,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L160,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014164,2025-08-21 03:25:36.785790,2025-08-21 03:25:36.708408,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014163,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L191,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014163,2025-08-21 03:25:36.785774,2025-08-21 03:25:36.708402,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014162,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L200,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014162,2025-08-21 03:25:36.785758,2025-08-21 03:25:36.708398,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014161,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L208,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014161,2025-08-21 03:25:36.785742,2025-08-21 03:25:36.708388,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014160,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L218,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014160,2025-08-21 03:25:36.785726,2025-08-21 03:25:36.708383,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014159,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L230,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014159,2025-08-21 03:25:36.785709,2025-08-21 03:25:36.708378,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014158,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L239,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014158,2025-08-21 03:25:36.785693,2025-08-21 03:25:36.708373,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014157,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L255,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014157,2025-08-21 03:25:36.785677,2025-08-21 03:25:36.708368,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014156,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L285,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014156,2025-08-21 03:25:36.785661,2025-08-21 03:25:36.708363,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014155,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L321,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014155,2025-08-21 03:25:36.785644,2025-08-21 03:25:36.708358,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014154,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L343,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014154,2025-08-21 03:25:36.785628,2025-08-21 03:25:36.708353,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014153,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L375,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014153,2025-08-21 03:25:36.785612,2025-08-21 03:25:36.708348,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014152,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L398,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014152,2025-08-21 03:25:36.785596,2025-08-21 03:25:36.708343,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014151,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L446,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014151,2025-08-21 03:25:36.785580,2025-08-21 03:25:36.708338,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014150,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L467,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014150,2025-08-21 03:25:36.785564,2025-08-21 03:25:36.708332,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014149,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L598,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014149,2025-08-21 03:25:36.785547,2025-08-21 03:25:36.708327,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014148,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L623,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014148,2025-08-21 03:25:36.785531,2025-08-21 03:25:36.708322,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014147,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/poll/route.ts#L667,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014147,2025-08-21 03:25:36.785515,2025-08-21 03:25:36.708317,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014146,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L84,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014146,2025-08-21 03:25:36.785499,2025-08-21 03:25:36.708312,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014145,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L101,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014145,2025-08-21 03:25:36.785483,2025-08-21 03:25:36.708307,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014144,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L136,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014144,2025-08-21 03:25:36.785467,2025-08-21 03:25:36.708301,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014143,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L355,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014143,2025-08-21 03:25:36.785450,2025-08-21 03:25:36.708293,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014142,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L391,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014142,2025-08-21 03:25:36.785433,2025-08-21 03:25:36.708288,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014141,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L486,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014141,2025-08-21 03:25:36.785390,2025-08-21 03:25:36.708283,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014140,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L496,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014140,2025-08-21 03:25:36.785373,2025-08-21 03:25:36.708277,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014139,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L523,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014139,2025-08-21 03:25:36.785357,2025-08-21 03:25:36.708272,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014138,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L573,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014138,2025-08-21 03:25:36.785341,2025-08-21 03:25:36.708267,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014137,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L587,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014137,2025-08-21 03:25:36.785325,2025-08-21 03:25:36.708262,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014136,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/generate/video/route.ts#L722,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014136,2025-08-21 03:25:36.785309,2025-08-21 03:25:36.708257,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014135,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/images/proxy/route.ts#L159,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014135,2025-08-21 03:25:36.785293,2025-08-21 03:25:36.708252,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014134,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/images/proxy/route.ts#L172,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014134,2025-08-21 03:25:36.785277,2025-08-21 03:25:36.708247,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014133,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/images/proxy/route.ts#L217,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014133,2025-08-21 03:25:36.785260,2025-08-21 03:25:36.708242,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014132,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/images/proxy/route.ts#L238,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014132,2025-08-21 03:25:36.785244,2025-08-21 03:25:36.708237,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014131,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/media/[id]/route.ts#L90,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014131,2025-08-21 03:25:36.785228,2025-08-21 03:25:36.708232,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014130,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/media/[id]/route.ts#L111,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014130,2025-08-21 03:25:36.785212,2025-08-21 03:25:36.708227,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014129,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/media/[id]/route.ts#L141,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014129,2025-08-21 03:25:36.785196,2025-08-21 03:25:36.708222,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014128,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-operation-status/route.ts#L58,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014128,2025-08-21 03:25:36.785180,2025-08-21 03:25:36.708217,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014127,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-operation-status/route.ts#L67,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014127,2025-08-21 03:25:36.785163,2025-08-21 03:25:36.708212,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014126,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-operation-status/route.ts#L80,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014126,2025-08-21 03:25:36.785147,2025-08-21 03:25:36.708207,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014125,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-operation-status/route.ts#L105,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014125,2025-08-21 03:25:36.785131,2025-08-21 03:25:36.708203,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014124,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-veo/route.ts#L34,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014124,2025-08-21 03:25:36.785115,2025-08-21 03:25:36.708198,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014123,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-veo/route.ts#L38,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014123,2025-08-21 03:25:36.785099,2025-08-21 03:25:36.708193,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014122,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/test-veo/route.ts#L89,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014122,2025-08-21 03:25:36.785083,2025-08-21 03:25:36.708188,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014121,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L29,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014121,2025-08-21 03:25:36.785066,2025-08-21 03:25:36.708183,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014120,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L94,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014120,2025-08-21 03:25:36.785050,2025-08-21 03:25:36.708178,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014119,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L109,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014119,2025-08-21 03:25:36.785034,2025-08-21 03:25:36.708173,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014118,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L181,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014118,2025-08-21 03:25:36.785018,2025-08-21 03:25:36.708168,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014117,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L186,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014117,2025-08-21 03:25:36.785002,2025-08-21 03:25:36.708163,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014116,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/user/images/route.ts#L187,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014116,2025-08-21 03:25:36.784985,2025-08-21 03:25:36.708158,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014115,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L179,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014115,2025-08-21 03:25:36.784969,2025-08-21 03:25:36.708153,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014114,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L219,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014114,2025-08-21 03:25:36.784953,2025-08-21 03:25:36.708148,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014113,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L290,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014113,2025-08-21 03:25:36.784937,2025-08-21 03:25:36.708143,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014112,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L327,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014112,2025-08-21 03:25:36.784921,2025-08-21 03:25:36.708138,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014111,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L364,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014111,2025-08-21 03:25:36.784905,2025-08-21 03:25:36.708133,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014110,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/api/video/proxy/route.ts#L386,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014110,2025-08-21 03:25:36.784889,2025-08-21 03:25:36.708128,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014109,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/app/landing-page-2/AiNextTemplate.tsx#L66,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014109,2025-08-21 03:25:36.784873,2025-08-21 03:25:36.708123,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014108,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L258,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014108,2025-08-21 03:25:36.784857,2025-08-21 03:25:36.708118,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014107,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L308,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014107,2025-08-21 03:25:36.784840,2025-08-21 03:25:36.708113,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014106,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L329,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014106,2025-08-21 03:25:36.784824,2025-08-21 03:25:36.708108,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014105,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L335,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014105,2025-08-21 03:25:36.784808,2025-08-21 03:25:36.708103,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014104,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L372,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014104,2025-08-21 03:25:36.784792,2025-08-21 03:25:36.708098,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014103,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L399,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014103,2025-08-21 03:25:36.784776,2025-08-21 03:25:36.708094,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014102,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L406,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014102,2025-08-21 03:25:36.784760,2025-08-21 03:25:36.708089,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014101,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L410,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014101,2025-08-21 03:25:36.784744,2025-08-21 03:25:36.708084,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014100,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L422,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014100,2025-08-21 03:25:36.784727,2025-08-21 03:25:36.708079,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014099,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L426,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014099,2025-08-21 03:25:36.784711,2025-08-21 03:25:36.708073,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014098,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L427,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014098,2025-08-21 03:25:36.784695,2025-08-21 03:25:36.708068,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014097,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L428,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014097,2025-08-21 03:25:36.784679,2025-08-21 03:25:36.708064,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014096,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L429,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014096,2025-08-21 03:25:36.784663,2025-08-21 03:25:36.708059,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014095,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L430,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014095,2025-08-21 03:25:36.784647,2025-08-21 03:25:36.708054,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014094,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L481,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014094,2025-08-21 03:25:36.784631,2025-08-21 03:25:36.708048,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014093,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L486,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014093,2025-08-21 03:25:36.784615,2025-08-21 03:25:36.708043,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014092,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L487,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014092,2025-08-21 03:25:36.784599,2025-08-21 03:25:36.708038,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014091,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L634,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014091,2025-08-21 03:25:36.784583,2025-08-21 03:25:36.708033,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014090,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L675,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014090,2025-08-21 03:25:36.784567,2025-08-21 03:25:36.708027,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014089,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L680,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014089,2025-08-21 03:25:36.784551,2025-08-21 03:25:36.708023,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014088,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L692,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014088,2025-08-21 03:25:36.784534,2025-08-21 03:25:36.708018,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014087,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L696,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014087,2025-08-21 03:25:36.784518,2025-08-21 03:25:36.708013,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014086,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L725,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014086,2025-08-21 03:25:36.784502,2025-08-21 03:25:36.708007,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014085,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/ai-image-generator/ImageGeneratorInterface.tsx#L726,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014085,2025-08-21 03:25:36.784486,2025-08-21 03:25:36.708002,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014084,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/gallery/UnifiedGallery.tsx#L221,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014084,2025-08-21 03:25:36.784470,2025-08-21 03:25:36.707997,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014083,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L825,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014083,2025-08-21 03:25:36.784454,2025-08-21 03:25:36.707992,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014082,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L902,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014082,2025-08-21 03:25:36.784438,2025-08-21 03:25:36.707986,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014081,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L999,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014081,2025-08-21 03:25:36.784421,2025-08-21 03:25:36.707981,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014080,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L1020,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014080,2025-08-21 03:25:36.784405,2025-08-21 03:25:36.707976,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014079,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L1069,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014079,2025-08-21 03:25:36.784389,2025-08-21 03:25:36.707969,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014078,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/EnhancedVideoGenerationInterface.tsx#L1106,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014078,2025-08-21 03:25:36.784373,2025-08-21 03:25:36.707964,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014077,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/VideoGenerator.tsx#L123,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014077,2025-08-21 03:25:36.784357,2025-08-21 03:25:36.707959,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014076,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/VideoGenerator.tsx#L241,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014076,2025-08-21 03:25:36.784340,2025-08-21 03:25:36.707954,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014075,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/VideoGenerator.tsx#L290,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014075,2025-08-21 03:25:36.784324,2025-08-21 03:25:36.707948,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014074,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/VideoGenerator.tsx#L313,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014074,2025-08-21 03:25:36.784308,2025-08-21 03:25:36.707943,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014073,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/components/video/VideoGenerator.tsx#L361,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014073,2025-08-21 03:25:36.784292,2025-08-21 03:25:36.707939,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014072,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L76,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014072,2025-08-21 03:25:36.784276,2025-08-21 03:25:36.707933,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014071,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L110,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014071,2025-08-21 03:25:36.784260,2025-08-21 03:25:36.707928,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014070,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L133,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014070,2025-08-21 03:25:36.784244,2025-08-21 03:25:36.707923,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014069,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L142,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014069,2025-08-21 03:25:36.784228,2025-08-21 03:25:36.707918,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014068,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L172,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014068,2025-08-21 03:25:36.784212,2025-08-21 03:25:36.707913,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014067,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L183,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014067,2025-08-21 03:25:36.784196,2025-08-21 03:25:36.707908,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014066,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L204,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014066,2025-08-21 03:25:36.784180,2025-08-21 03:25:36.707903,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014065,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L261,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014065,2025-08-21 03:25:36.784164,2025-08-21 03:25:36.707898,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014064,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L286,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014064,2025-08-21 03:25:36.784148,2025-08-21 03:25:36.707894,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014063,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L295,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014063,2025-08-21 03:25:36.784132,2025-08-21 03:25:36.707889,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014062,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L326,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014062,2025-08-21 03:25:36.784115,2025-08-21 03:25:36.707884,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014061,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L337,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014061,2025-08-21 03:25:36.784099,2025-08-21 03:25:36.707878,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014060,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L369,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014060,2025-08-21 03:25:36.784083,2025-08-21 03:25:36.707873,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014059,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L375,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014059,2025-08-21 03:25:36.784067,2025-08-21 03:25:36.707868,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014058,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L387,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014058,2025-08-21 03:25:36.784050,2025-08-21 03:25:36.707863,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014057,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L398,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014057,2025-08-21 03:25:36.784034,2025-08-21 03:25:36.707858,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014056,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bfl-service.ts#L435,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014056,2025-08-21 03:25:36.784017,2025-08-21 03:25:36.707852,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014055,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L50,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014055,2025-08-21 03:25:36.783847,2025-08-21 03:25:36.707847,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014054,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L88,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014054,2025-08-21 03:25:36.783831,2025-08-21 03:25:36.707842,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014053,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L104,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014053,2025-08-21 03:25:36.783815,2025-08-21 03:25:36.707837,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014052,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L123,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014052,2025-08-21 03:25:36.783799,2025-08-21 03:25:36.707832,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014051,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L127,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014051,2025-08-21 03:25:36.783783,2025-08-21 03:25:36.707827,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014050,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L139,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014050,2025-08-21 03:25:36.783767,2025-08-21 03:25:36.707822,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014049,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L164,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014049,2025-08-21 03:25:36.783751,2025-08-21 03:25:36.707816,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014048,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-service.ts#L175,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014048,2025-08-21 03:25:36.783734,2025-08-21 03:25:36.707811,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014047,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L160,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014047,2025-08-21 03:25:36.783718,2025-08-21 03:25:36.707806,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014046,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L171,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014046,2025-08-21 03:25:36.783702,2025-08-21 03:25:36.707801,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014045,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L265,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014045,2025-08-21 03:25:36.783686,2025-08-21 03:25:36.707796,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014044,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L316,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014044,2025-08-21 03:25:36.783670,2025-08-21 03:25:36.707791,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014043,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L329,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014043,2025-08-21 03:25:36.783654,2025-08-21 03:25:36.707786,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014042,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L373,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014042,2025-08-21 03:25:36.783638,2025-08-21 03:25:36.707781,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014041,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L403,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014041,2025-08-21 03:25:36.783621,2025-08-21 03:25:36.707776,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014040,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L418,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014040,2025-08-21 03:25:36.783594,2025-08-21 03:25:36.707771,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014039,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L483,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014039,2025-08-21 03:25:36.783577,2025-08-21 03:25:36.707765,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014038,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L520,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014038,2025-08-21 03:25:36.783561,2025-08-21 03:25:36.707761,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014037,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L533,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014037,2025-08-21 03:25:36.783545,2025-08-21 03:25:36.707756,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014036,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L553,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014036,2025-08-21 03:25:36.783530,2025-08-21 03:25:36.707751,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014035,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L608,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014035,2025-08-21 03:25:36.783513,2025-08-21 03:25:36.707746,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014034,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L673,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014034,2025-08-21 03:25:36.783497,2025-08-21 03:25:36.707741,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014033,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L681,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014033,2025-08-21 03:25:36.783481,2025-08-21 03:25:36.707736,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014032,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L784,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014032,2025-08-21 03:25:36.783465,2025-08-21 03:25:36.707731,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014031,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L793,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014031,2025-08-21 03:25:36.783449,2025-08-21 03:25:36.707726,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014030,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L839,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014030,2025-08-21 03:25:36.783433,2025-08-21 03:25:36.707720,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014029,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/bytedance-video-service.ts#L849,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014029,2025-08-21 03:25:36.783417,2025-08-21 03:25:36.707715,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014028,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L157,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014028,2025-08-21 03:25:36.783401,2025-08-21 03:25:36.707710,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014027,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L203,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014027,2025-08-21 03:25:36.783385,2025-08-21 03:25:36.707705,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014026,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L275,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014026,2025-08-21 03:25:36.783369,2025-08-21 03:25:36.707700,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014025,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L282,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014025,2025-08-21 03:25:36.783353,2025-08-21 03:25:36.707695,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014024,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L295,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014024,2025-08-21 03:25:36.783337,2025-08-21 03:25:36.707689,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014023,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L326,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014023,2025-08-21 03:25:36.783321,2025-08-21 03:25:36.707684,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014022,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L327,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014022,2025-08-21 03:25:36.783305,2025-08-21 03:25:36.707679,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014021,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L358,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014021,2025-08-21 03:25:36.783289,2025-08-21 03:25:36.707674,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014020,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L394,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014020,2025-08-21 03:25:36.783273,2025-08-21 03:25:36.707668,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014019,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L472,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014019,2025-08-21 03:25:36.783257,2025-08-21 03:25:36.707658,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014018,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L517,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014018,2025-08-21 03:25:36.783240,2025-08-21 03:25:36.707653,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014017,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/google-veo.ts#L531,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014017,2025-08-21 03:25:36.783223,2025-08-21 03:25:36.707648,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014016,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L154,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014016,2025-08-21 03:25:36.783206,2025-08-21 03:25:36.707642,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014015,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L166,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014015,2025-08-21 03:25:36.783190,2025-08-21 03:25:36.707632,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014014,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L230,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014014,2025-08-21 03:25:36.783173,2025-08-21 03:25:36.707627,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014013,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L266,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014013,2025-08-21 03:25:36.783156,2025-08-21 03:25:36.707620,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014012,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L274,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014012,2025-08-21 03:25:36.783139,2025-08-21 03:25:36.707606,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014011,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L284,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014011,2025-08-21 03:25:36.783122,2025-08-21 03:25:36.707601,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014010,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L320,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014010,2025-08-21 03:25:36.783105,2025-08-21 03:25:36.707595,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014009,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L338,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014009,2025-08-21 03:25:36.783088,2025-08-21 03:25:36.707589,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014008,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L389,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014008,2025-08-21 03:25:36.783072,2025-08-21 03:25:36.707584,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014007,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L406,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014007,2025-08-21 03:25:36.783054,2025-08-21 03:25:36.707579,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014006,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L418,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014006,2025-08-21 03:25:36.783037,2025-08-21 03:25:36.707574,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014005,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L426,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014005,2025-08-21 03:25:36.783020,2025-08-21 03:25:36.707569,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014004,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L461,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014004,2025-08-21 03:25:36.783003,2025-08-21 03:25:36.707564,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014003,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L474,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014003,2025-08-21 03:25:36.782985,2025-08-21 03:25:36.707559,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014002,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L526,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014002,2025-08-21 03:25:36.782968,2025-08-21 03:25:36.707553,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014001,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L563,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014001,2025-08-21 03:25:36.782951,2025-08-21 03:25:36.707548,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237014000,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L568,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237014000,2025-08-21 03:25:36.782934,2025-08-21 03:25:36.707543,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013999,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L579,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013999,2025-08-21 03:25:36.782918,2025-08-21 03:25:36.707538,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013998,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L596,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013998,2025-08-21 03:25:36.782901,2025-08-21 03:25:36.707533,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013997,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L607,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013997,2025-08-21 03:25:36.782883,2025-08-21 03:25:36.707528,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013996,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L616,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013996,2025-08-21 03:25:36.782867,2025-08-21 03:25:36.707522,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013995,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L635,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013995,2025-08-21 03:25:36.782850,2025-08-21 03:25:36.707517,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013994,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L642,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013994,2025-08-21 03:25:36.782833,2025-08-21 03:25:36.707512,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013993,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/minimax-video-service.ts#L653,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013993,2025-08-21 03:25:36.782816,2025-08-21 03:25:36.707507,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013992,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L48,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013992,2025-08-21 03:25:36.782799,2025-08-21 03:25:36.707502,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013991,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L59,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013991,2025-08-21 03:25:36.782782,2025-08-21 03:25:36.707496,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013990,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L60,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013990,2025-08-21 03:25:36.782764,2025-08-21 03:25:36.707491,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013989,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L64,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013989,2025-08-21 03:25:36.782747,2025-08-21 03:25:36.707486,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013988,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L91,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013988,2025-08-21 03:25:36.782730,2025-08-21 03:25:36.707481,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013987,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L130,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013987,2025-08-21 03:25:36.782713,2025-08-21 03:25:36.707476,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013986,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L162,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013986,2025-08-21 03:25:36.782696,2025-08-21 03:25:36.707471,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013985,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L172,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013985,2025-08-21 03:25:36.782679,2025-08-21 03:25:36.707466,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013984,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L180,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013984,2025-08-21 03:25:36.782662,2025-08-21 03:25:36.707461,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013983,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L185,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013983,2025-08-21 03:25:36.782644,2025-08-21 03:25:36.707456,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013982,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L192,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013982,2025-08-21 03:25:36.782627,2025-08-21 03:25:36.707450,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013981,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L216,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013981,2025-08-21 03:25:36.782610,2025-08-21 03:25:36.707445,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013980,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/vertex-ai.ts#L217,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013980,2025-08-21 03:25:36.782593,2025-08-21 03:25:36.707439,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013979,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/video-queue.ts#L131,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013979,2025-08-21 03:25:36.782576,2025-08-21 03:25:36.707435,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013978,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/video-queue.ts#L177,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013978,2025-08-21 03:25:36.782559,2025-08-21 03:25:36.707430,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013977,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/video-queue.ts#L261,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013977,2025-08-21 03:25:36.782542,2025-08-21 03:25:36.707425,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013976,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/video-queue.ts#L314,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013976,2025-08-21 03:25:36.782525,2025-08-21 03:25:36.707419,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013975,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"This is a low-confidence log-format finding: it flagged the template literal containing job.id passed as the first argument to console.error. In this code path job.id is a DB/system identifier (not user-supplied) and the risk is limited to log-forging (low impact), so it is not worth blocking as a security fix here.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/services/video-queue.ts#L416,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013975,2025-08-21 03:25:36.782507,2025-08-21 03:25:36.707414,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013974,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,True positive,,,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L114,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013974,2025-08-21 03:25:36.782490,2025-08-21 03:25:36.707409,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013973,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The format string is not attacker-controlled: r2RequestId is generated from Date.now() and Math.random().toString(36) (digits/letters only), so there is no user-tainted input that could inject format specifiers into the console.log format string. The rule intended to catch non-literal format strings, but this instance is internally generated and not exploitable.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L152,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013973,2025-08-21 03:25:36.782472,2025-08-21 03:25:36.707403,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013972,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The rule flagged a non-literal template used in console.log, but the interpolated value (r2RequestId) is internally generated (Date.now() + Math.random().toString(36)...), not user-controlled, so there's no format-string injection source; additionally other data is passed as a separate object argument rather than injected into the format string. This is a low-confidence false positive in this context.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L158,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013972,2025-08-21 03:25:36.782455,2025-08-21 03:25:36.707398,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013971,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The logged template string only embeds r2RequestId derived from Date.now()/Math.random (not user-controlled), so untrusted input does not reach the format string; this is a low-confidence rule match and a false positive in this context.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L240,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013971,2025-08-21 03:25:36.782437,2025-08-21 03:25:36.707392,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013970,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The interpolated value (r2RequestId) is generated internally (Date.now + Math.random) and not attacker-controlled, and the logs pass user data as an object argument rather than relying on format specifiers — so format-string injection is not feasible here.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L255,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013970,2025-08-21 03:25:36.782420,2025-08-21 03:25:36.707386,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013969,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The flagged format string (r2RequestId in the template literal) is generated locally from Date.now()/Math.random and not user-controlled, so there is no reachable format-string injection; other potentially user values are logged as separate object args rather than as the first formatted string. This is a low-confidence false positive and not reasonably exploitable in this context.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L265,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013969,2025-08-21 03:25:36.782402,2025-08-21 03:25:36.707380,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013968,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The rule aims to catch user-controlled format strings, but r2RequestId is built from Date.now() and Math.random() (not attacker-controlled) so no tainted input reaches the format string; other dynamic values are logged as separate objects. This is a low-confidence false positive and not a security risk in this context.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/r2-client.ts#L266,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013968,2025-08-21 03:25:36.782382,2025-08-21 03:25:36.707374,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013967,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The logged template embeds an internally-generated tosRequestId (Date.now() + Math.random()), not user-controlled input, so there's no attacker-controlled format string going into console.log/util.format — this is a low-confidence false positive and not exploitable here.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/tos-client.ts#L178,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013967,2025-08-21 03:25:36.782361,2025-08-21 03:25:36.707365,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
237013966,javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring,Code,Low,Open,Low,security,False,False positive,"The rule intended to flag non-literal format strings to console/util.format, but here the only dynamic part is tosRequestId which is generated internally (Date.now()/Math.random) and not attacker-controlled, so there is no format-string injection risk. The log message is mostly constant and the error is passed as a separate argument, making this a low-risk false positive.",,imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5,https://github.com/imagegen1/gensy-version-5/blob/645d474d6c2b9b646a50fe88a052924c02fa2ca5/src/lib/storage/tos-client.ts#L244,https://semgrep.dev/orgs/imagegencreation-personal-org/findings/237013966,2025-08-21 03:25:36.782307,2025-08-21 03:25:36.707339,refs/heads/master,,,,"Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string."
