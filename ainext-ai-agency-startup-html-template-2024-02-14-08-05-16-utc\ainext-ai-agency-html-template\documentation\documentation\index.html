<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="">
        <meta name="keywords" content="">
        <title>Ainext</title>
        <!-- Style CSS -->
        <link href="css/all.css" rel="stylesheet">
        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css?family=Raleway:100,300,400,500%7CLato:300,400" rel="stylesheet" type="text/css">
		<link rel="icon" type="image/png" href="images/favicon.png">
    </head>

    <body data-spy="scroll" data-target=".sidebar" data-offset="200">

        <!-- Sidebar -->
        <aside class="sidebar sidebar-boxed sidebar-dark">  
            <ul class="nav sidenav dropable">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#getting_started" title="Getting Started">Getting Started</a></li>
                <li><a href="#head_css_structure" title="HEAD CSS Structure">HEAD CSS Structure</a></li>
                <li><a href="#javascript_structure" title="Javascript Structure">Javascript Structure</a></li>
                <li><a href="#html_structure" title="HTML Structure">HTML Structure</a></li>
                <li><a href="#fonts_used" title="Fonts used">Fonts used</a></li>
                <li><a href="#color-change-option" title="Main Color Change Option">Main Color Change</a></li>
                <li><a href="#credits" title="Credits">Credits</a></li>
                <li><a href="#supports" title="Support">Support</a></li>
            </ul>
        </aside>
        <!-- END Sidebar -->

<header class="site-header navbar-transparent"> 
    <!-- Banner -->
    <div class="banner auto-size" style="background-color: #7f00ff">
        <div class="container-fluid text-white" style="background-color: #7f00ff;padding-left: 0;">
            <h1 style="margin: 0; padding: 30px 0;"><strong>Ainext</strong> Documentation</h1>
        </div>
    </div>
    <!-- END Banner --> 
</header>
        
<main class="container-fluid"> 
    <!-- Main content -->
    <article class="main-content" role="main">
        <section id="introduction">
            <h2 style="margin-top: 0; line-height: 45px;">Introduction</h2>
            <h6 style="margin-top: 35px; margin-bottom: 15px;"><strong>Template Features:</strong></h6>
            <ul>
                <li>Image Generator</li>
                <li>Easy to customize</li>
                <li>HTML5 & CSS3</li>
                <li>Responsive Design & Retina ready & touch gestures on sliders & mobile friendly components : designed for best mobile experience</li>
                <li>W3 Valid</li>
                <li>Retina ready</li>
                <li>RemixIcon <strong>Free icons</strong></li>
                <li>Powered with Bootstrap <strong>(v5.3.1)</strong></li>
                <li>Powered with jQuery <strong>(v3.6.1)</strong></li>
                <li>Smooth animation</li>
                <li><strong>Well documented</strong></li>
                <li>Crossbrowser Compatible with <strong>Edge, IE10+, Firefox, Opera, Chrome</strong></li>
            </ul>
        </section>
               
        <section id="getting_started">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">Getting Started</h3>
            </div>
            <p>The template folder <strong>Ainext</strong> is available inside the downloaded zip file. Need to unzip the zip file to find the template also the documentation folder.</p>
            <p>The <strong>files and folders structure</strong> is similar to the following:</p>
            <ol>
                <li><strong>assets/css</strong> — folder with CSS files.</li>
                <li><strong>assets/fonts</strong> — folder with fonts files.</li>
                <li><strong>assets/img</strong> — folder with image files.</li>
                <li><strong>assets/js</strong> — folder with Javascript files.</li>
            </ol>
            <p>Upload the template files to the server with the help of one of the FTP-clients like FileZilla.</p>
            <h6 style="margin-top: 20px;"><strong>HTML Files:</strong></h6>
            <ol>
                <li>Image Generator (<b>index.html</b>)</li>
                <li>About Page (<b>about.html</b>)</li>
                <li>Team Page (<b>team.html</b>)</li>
                <li>Portfolio Page (<b>portfolio.html</b>)</li>
                <li>Pricing Page (<b>pricing.html</b>)</li>
                <li>Blog Page (<b>blog.html</b>)</li>
                <li>Blog Details (<b>blog-details.html</b>)</li>
                <li>Blog Sidebar (<b>blog-sidebar.html</b>)</li>
                <li>Privacy Policy Page (<b>privacy-policy.html</b>)</li>
                <li>Not Found Page (<b>not-found.html</b>)</li>
                <li>Terms conditions Page (<b>terms-conditions.html</b>)</li>
                <li>Contact (<b>contact.html</b>)</li>
            </ol>
            <h6 style="margin-top: 20px;"><strong>Files Edit & Upload:</strong></h6>
            <p>Individual pages can be customized by opening in a code editor such as VS Code. Once all the customization completed then to make the website live you need to upload the updated project files to the hosting server for your own domain. The files can be uploaded using FTP client such as FileZilla.</p>
        </section>
                
        <section id="head_css_structure">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">HEAD CSS Structure</h3>
            </div>
            <p>Followings are the <strong>css</strong> files which loaded inside the <strong>Head Section</strong>:</p>
<pre class="line-numbers">
<code class="language-markup">
<div>
&lt;!-- Links of CSS files --&gt;
<div>&lt;link rel="stylesheet" href="assets/css/bootstrap.min.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/owl.carousel.min.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/owl.theme.default.min.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/remixicon.min.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/flaticon.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/aos.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/style.css"&gt;</div>
<div>&lt;link rel="stylesheet" href="assets/css/responsive.css"&gt;</div>
</div>
</code>
</pre class="brush:css">
        </section>

        <section id="javascript_structure">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">Javascript Structure</h3>
            </div>
            <p>Followings are the <strong>JS</strong> files which loaded before the end of <strong>HEAD or BODY Section:</strong>.</p>
<pre class="line-numbers">
<code class="language-markup">
<div>
&lt;!-- Links of JS files --&gt;
<div>&lt;script src="assets/js/jquery.min.js"&gt;&lt;/script&gt;</div>
<div>&lt;script src="assets/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;</div>
<div>&lt;script src="assets/js/aos.js"&gt;&lt;/script&gt;</div>
<div>&lt;script src="assets/js/owl.carousel.min.js"&gt;&lt;/script&gt;</div>
<div>&lt;script src="assets/js/ainext.js"&gt;&lt;/script&gt;</div>
</div>
</code>
</pre>
        </section>
                
        <section id="html_structure" style="padding-bottom: 0;">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">HTML Structure</h3>
            </div>
            The <strong>Ainext</strong> is a responsive template and is based on the <strong>Bootstrap Framework</strong>. For more information you can check the <a target="_blank" href="https://getbootstrap.com/"><strong>Bootstrap CSS</strong></a>.
            <p>The general template structure is the same throughout the template and each of the part is under a section with a section id name. Here is the <strong>general structure:</strong></p>
<pre class="line-numbers">
<code class="language-markup">
<div>
<div>&lt;!-- page title --&gt;</div>
<div>&lt;section-banner"&gt;</div>
<div>   &lt;div class="container"&gt;</div>
<div>       &lt;div class="section-banner-title"&gt;</div>
<div>           &lt;h1&gt;About us&lt;/h1&gt;</div>
<div>           &lt;ul class="breadcrumb" &gt;
                <div> &lt;li class="breadcrumb-item"&gt;&lt;a href="#"&gt;Home&lt;/a&gt;&lt;/li&gt;</div> 
                <div> &lt;li class="breadcrumb-item"&gt;&lt;/li&gt;</div> 
                <div> &lt;li class="breadcrumb-item active"&gt;About us&lt;/li&gt;</div> 
            &lt;/ul&gt;</div>
<div>       &lt;/div&gt;</div>
<div>   &lt;/div&gt;</div>
<div>&lt;/div&gt;</div>
<div>&lt;!-- page title end--&gt;</div>
</div>
</code>
</pre>
        </section>

        <section id="fonts_used">
            <div class="page-header">
                <h3 style="margin-bottom: 12px;">Fonts Used</h3 >
            </div>
            <p>By default, the template loads <strong>Raleway</strong> and <strong>Plus Jakarta</strong> Fonts.</p>
            <p>
                Font code can be found in the &quot;<strong>style.css</strong>&quot; file path: assets/css/style.css
            </p>
<pre class="line-numbers">
    <code class="language-css">
    @font-face {
        font-family: "Raleway";
        @import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,900&display=swap");
        font-weight: 300;
        font-style: normal;
    }

    @font-face {
        font-family: "Plus Jakarta Sans";
        @import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,900&display=swap");
        font-weight: 300;
        font-style: normal;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
    font-family: "Plus Jakarta Sans", sans-serif;
    }

    body {
        font-family: "Raleway", sans-serif;
    }
    </code>
</pre>
        </section>

        <section id="fonts_used">
            <div class="page-header">
                <h3 style="margin-bottom: 12px;">How Can You Use Google Font</h3 >
            </div>
            <p>Tou Use Google Fonts, You Need to Update the Code From &quot;<strong>style.css</strong>&quot; The font can be changed based on the website needs. <a target="_blank" href="https://fonts.google.com/">Google Fonts</a></p>
            <p>
                Font code need to add in &quot;<strong>style.css</strong>&quot; file path: assets/css/style.css
            </p>
<pre class="line-numbers">
<code class="language-css">
    @import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,900&display=swap");

body {
    font-family: "Raleway", sans-serif;
}
</code>
</pre>
        </section>

        <section id="color-change-option">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">Main Color Change Option</h3>
            </div>
            <p>File path: assets/css/style.css</p>

<pre class="line-numbers">
<code class="language-css">
:root {
    --main_color:#7f00ff;
    --content_color:#9094a6;
    --white_color:#fff;
    --font-size: 17px;
    --transition:.4s;
}
</code>
</pre>
    <p><b>Note:</b> After CSS file run then it will be work</p>
        </section>
                    
        <section id="how-to-change-image">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">How to Change Image?</h3>
            </div>
            <p>Please go to the image file 'path: assets/img/...' replace the image which one you want following placeholder image size & name</p>
        </section>
				 
        <section>
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">How to Add New Updates of Existing Template</h3>
            </div>
            <p>After a certain time, we will update our project based on the latest technology but in your template, you need to update on your own if you want.</p>
        </section>
    
        <section id="credits">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">Credits</h3>
            </div>
            <p><strong>Note:</strong> All <strong>images</strong> are used for <strong>preview</strong> purpose only and <strong>not included</strong> in the <strong>final purchase</strong> files.</p>
            <p><strong>Images from:</strong><br />
                <a target="_blank" href="https://www.freepik.com/">https://freepik.com/</a><br />
                <a target="_blank" href="https://leonardo.ai/">https://leonardo.ai/</a><br />
            </p>
            <p>
                <strong>Custom Fonts:</strong><br/>
                <a href="https://fonts.google.com/specimen/Plus+Jakarta+Sans?query=Plus+Jakarta">Plus Jakarta Sans</a><br>
                <a href="https://fonts.google.com/specimen/Raleway?query=Raleway">Raleway</a><br>
            </p>
            <p><strong>IconFont:</strong><br>
                <a target="_blank" href="https://remixicon.com/">Remix Icon</a><br></p>
                <a target="_blank" href="https://www.flaticon.com/">FLATICON</a><br></p>
        </section>
        
        <section id="supports">
            <div class="page-header">
                <h3 style="margin-top: 0; margin-bottom: 12px;">Support</h3>
            </div>
            <p>For any query, please use the item's comment section on ThemeForest. Thanks!</p>
        </section>
    </article>
    <!-- END Main content --> 
</main>

    <!-- Footer -->
    <footer class="site-footer text-center">
        <div class="container-fluid"> <a id="scroll-up" href="#"><i class="fa fa-angle-up"></i></a>
            <p>Ainext Template | All Rights Reserved</p>
        </div>
    </footer>
    <!-- END Footer -->
    
    <!-- Scripts -->
    <script src="js/all.js"></script>
    <script src="js/main.js"></script>
</body>
</html>