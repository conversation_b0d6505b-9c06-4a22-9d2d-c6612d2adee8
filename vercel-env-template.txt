# Vercel Environment Variables Template
# Copy these variables to your Vercel dashboard under Settings > Environment Variables

# ===== ESSENTIAL VARIABLES (REQUIRED) =====

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-vercel-domain.vercel.app
NEXT_PUBLIC_APP_NAME=Gensy
NODE_ENV=production

# Clerk Authentication (IMPORTANT: Use PRODUCTION keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_production_clerk_key_here
CLERK_SECRET_KEY=sk_live_your_production_clerk_secret_here
CLERK_WEBHOOK_SECRET=whsec_your_production_webhook_secret_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/onboarding
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/onboarding

# Supabase Configuration (IMPORTANT: Use PRODUCTION database)
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_production_supabase_service_role_key_here

# Google Cloud / Vertex AI Configuration (CRITICAL: Use Base64 for Vercel)
GOOGLE_CLOUD_PROJECT_ID=gensy-final-464206
GOOGLE_CREDENTIALS_BASE64=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_CLOUD_STORAGE_BUCKET=gensy-final

# Cloudflare R2 Storage Configuration (IMPORTANT: Use PRODUCTION bucket)
CLOUDFLARE_R2_ACCESS_KEY_ID=your_production_r2_access_key_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_production_r2_secret_key_here
CLOUDFLARE_R2_BUCKET_NAME=gensy-production
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_PUBLIC_URL=https://your-production-domain.com

# ===== API KEYS (REQUIRED FOR FUNCTIONALITY) =====

# BytePlus API Configuration
BYTEPLUS_API_KEY=eca88181-4393-49db-8815-bb1c9d03d829
BYTEPLUS_API_ENDPOINT=https://ark.ap-southeast.bytepluses.com/api/v3

# TOS Configuration (for ByteDance video storage)
TOS_ACCESS_KEY_ID=your_production_tos_access_key
TOS_SECRET_ACCESS_KEY=your_production_tos_secret_key
TOS_BUCKET_NAME=your_production_tos_bucket
TOS_ENDPOINT=https://tos-s3-cn-beijing.volces.com
TOS_REGION=cn-beijing

# Replicate API Configuration
REPLICATE_API_TOKEN=your_production_replicate_token_here

# OpenRouter API Configuration
OPENROUTER_API_KEY=your_production_openrouter_key_here

# ===== OPTIONAL SERVICES =====

# PhonePe Payment Gateway (if using payments)
PHONEPE_MERCHANT_ID=your_production_merchant_id
PHONEPE_SALT_KEY=your_production_salt_key
PHONEPE_SALT_INDEX=1
PHONEPE_ENVIRONMENT=production
PHONEPE_CALLBACK_URL=/api/payments/callback

# ===== IMPORTANT NOTES =====
# 1. DO NOT use development/test API keys in production
# 2. GOOGLE_CREDENTIALS_BASE64 is preferred over GOOGLE_APPLICATION_CREDENTIALS for Vercel
# 3. Update all URLs to use your actual Vercel domain
# 4. Use production databases and storage buckets
# 5. Test all endpoints after deployment
