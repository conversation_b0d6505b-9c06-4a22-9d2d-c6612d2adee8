# Gensy AI Creative Suite - Development Environment
# This file contains development environment variables
# DO NOT commit this file to version control

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Gensy
NODE_ENV=development

# Clerk Authentication (Development Keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_bGVuaWVudC1tb3VzZS0zMi5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_GV9Q8PdJvbp8qbFgv15WljTzklSM6uB2SPSatq81Eu
CLERK_WEBHOOK_SECRET=whsec_development_webhook_secret_placeholder
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/onboarding
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/onboarding

# Supabase Configuration (Development)
NEXT_PUBLIC_SUPABASE_URL=https://fhwowycnuoakmctwywpm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.i7q3BMm-r5WuV1OcS3wgdaKL9ThaR3_C5VaWIIUyzIw
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZod293eWNudW9ha21jdHd5d3BtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA4NzQ0MiwiZXhwIjoyMDY1NjYzNDQyfQ.lbei0rra5MVOs5lipREfo20exZZ7YyHm2AwdyHL-tQw

# Google Cloud / Vertex AI Configuration (Development)
GOOGLE_CLOUD_PROJECT_ID=gensy-final-464206
GOOGLE_CLOUD_LOCATION=us-central1
# Option 1: File path (for local development)
GOOGLE_APPLICATION_CREDENTIALS=sprint/gensy-final-464206-1327695f628d.json
# Option 2: Base64 encoded credentials (for production/deployment)
GOOGLE_CREDENTIALS_BASE64=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GOOGLE_CLOUD_STORAGE_BUCKET=gensy-final

# Cloudflare R2 Storage Configuration (Development)
CLOUDFLARE_R2_ACCESS_KEY_ID=b054bd67ca13e5e88d93977fa807d599
CLOUDFLARE_R2_SECRET_ACCESS_KEY=cd2fc3c8a60402ec488e7ce082b21b7b25aee92729763f5eaf89a8b40c4a555a
CLOUDFLARE_R2_BUCKET_NAME=gensy
CLOUDFLARE_R2_ENDPOINT=https://55c39fd5a91da63f331a9af2bb8423d5.r2.cloudflarestorage.com
CLOUDFLARE_R2_PUBLIC_URL=https://pub-b73a86bd5ccf4cc7bba9daf3c7fb363e.r2.dev

# Replicate API Configuration (Development)
REPLICATE_API_TOKEN=****************************************

# OpenRouter API Configuration (for prompt enhancement with DeepSeek-R1)
OPENROUTER_API_KEY=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

# BytePlus ModelArk API Configuration (for Bytedance-Seedream-3.0-t2i and Seedream-1.0-lite-t2v)
BYTEPLUS_API_KEY=eca88181-4393-49db-8815-bb1c9d03d829
BYTEPLUS_API_ENDPOINT=https://ark.ap-southeast.bytepluses.com/api/v3

# TOS (Torch Object Storage) Configuration for ByteDance video storage
TOS_ACCESS_KEY_ID=AKAPZGExZTUwYTBmZDdkNDU2Njk1MmE3YThiMzVmNGNmZjI
TOS_SECRET_ACCESS_KEY=WXpFd04yWXpZV0ZrWW1Rd05EWmlORGd6TTJKaE9UQmlNelZtTkdZNFptVQ==
TOS_BUCKET_NAME=gensy-video-photo-app-user-uploads-2025
TOS_ENDPOINT=https://tos-ap-southeast-1.ibytepluses.com
TOS_REGION=Asia Pacific (Johor)

# PhonePe Payment Gateway Configuration (Development/Sandbox)
PHONEPE_MERCHANT_ID=GENSYONLINE
PHONEPE_SALT_KEY=development_salt_key_placeholder
PHONEPE_SALT_INDEX=1
PHONEPE_ENVIRONMENT=sandbox
PHONEPE_CALLBACK_URL=/api/payments/callback

# Security Keys (Development - Generate new ones for production)
NEXTAUTH_SECRET=dev_nextauth_secret_32_chars_exactly
JWT_SECRET=dev_jwt_secret_key_32_chars_exactly
ENCRYPTION_KEY=dev_encryption_key_32_chars_exac

# Feature Flags (Development)
NEXT_PUBLIC_ENABLE_VIDEO_GENERATION=true
NEXT_PUBLIC_ENABLE_IMAGE_UPSCALING=true
NEXT_PUBLIC_ENABLE_BATCH_PROCESSING=true
NEXT_PUBLIC_MAX_FILE_SIZE_MB=10
NEXT_PUBLIC_MAX_CREDITS_FREE_TIER=10

# Development Testing
NEXT_PUBLIC_TEST_MODE=false