<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FLUX Kontext Images Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #050913;
            color: white;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-card {
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
            background: #1a1a2e;
        }
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
        }
        .image-card .info {
            padding: 15px;
        }
        .image-card h3 {
            margin: 0 0 5px 0;
            color: #00bcd4;
        }
        .image-card p {
            margin: 0;
            color: #ccc;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #2d5a2d; border: 1px solid #4caf50; }
        .error { background: #5a2d2d; border: 1px solid #f44336; }
    </style>
</head>
<body>
    <h1>🎯 FLUX Kontext Pro Images Test</h1>
    <p>Testing the 5 local FLUX Kontext images used in the interactive selector component.</p>

    <div class="image-grid">
        <div class="image-card">
            <img src="/flux-kontext-1.png" alt="Luxury Tent" onerror="this.parentElement.classList.add('error-img')">
            <div class="info">
                <h3>Option 1: Luxury Tent</h3>
                <p>Cozy glamping under the stars</p>
                <p><strong>File:</strong> flux-kontext-1.png</p>
            </div>
        </div>

        <div class="image-card">
            <img src="/flux-kontext-2.png" alt="Campfire Feast" onerror="this.parentElement.classList.add('error-img')">
            <div class="info">
                <h3>Option 2: Campfire Feast</h3>
                <p>Gourmet s'mores & stories</p>
                <p><strong>File:</strong> flux-kontext-2.png</p>
            </div>
        </div>

        <div class="image-card">
            <img src="/flux-kontext-3.png" alt="Lakeside Retreat" onerror="this.parentElement.classList.add('error-img')">
            <div class="info">
                <h3>Option 3: Lakeside Retreat</h3>
                <p>Private dock & canoe rides</p>
                <p><strong>File:</strong> flux-kontext-3.png</p>
            </div>
        </div>

        <div class="image-card">
            <img src="/flux-kontext-4.png" alt="Mountain Spa" onerror="this.parentElement.classList.add('error-img')">
            <div class="info">
                <h3>Option 4: Mountain Spa</h3>
                <p>Outdoor sauna & hot tub</p>
                <p><strong>File:</strong> flux-kontext-4.png</p>
            </div>
        </div>

        <div class="image-card">
            <img src="/flux-kontext-5.png" alt="Guided Adventure" onerror="this.parentElement.classList.add('error-img')">
            <div class="info">
                <h3>Option 5: Guided Adventure</h3>
                <p>Expert-led nature tours</p>
                <p><strong>File:</strong> flux-kontext-5.png</p>
            </div>
        </div>
    </div>

    <div class="status success">
        <strong>✅ Implementation Complete:</strong> All 5 FLUX Kontext images have been configured to use local files instead of Unsplash URLs.
    </div>

    <div class="status">
        <strong>🔄 Future Updates:</strong> To replace any image, simply replace the corresponding PNG file in the /public directory. No code changes required!
    </div>

    <script>
        // Add error styling for failed images
        const style = document.createElement('style');
        style.textContent = `
            .error-img {
                border-color: #f44336 !important;
            }
            .error-img img {
                background: #5a2d2d;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-img img::after {
                content: "❌ Image not found";
                color: #f44336;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
